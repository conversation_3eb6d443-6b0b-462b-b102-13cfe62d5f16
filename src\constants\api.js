// API Configuration
export const API_CONFIG = {
  BASE_URL: 'https://api-partner.pay2s.vn',
  TIMEOUT: 30000, // 30 seconds
  RETRY_ATTEMPTS: 3,
};

// API Endpoints - Based on BankHub API Collection
export const API_ENDPOINTS = {
  // 🔐 Authentication
  AUTH: {
    LOGIN: '/partner/login',
    REGISTER: '/partner/register',
    FORGOT_PASSWORD: '/partner/forgot-password',
    RESET_PASSWORD: '/partner/reset-password',
    CURRENT_USER: '/me',
  },

  // 👤 Partner Management
  PARTNER: {
    PROFILE: '/partner/profile',
    UPDATE_PROFILE: '/partner/profile',
    CHANGE_PASSWORD: '/partner/password',
    ACTIVITIES: '/partner/activities',
    REGENERATE_CREDENTIALS: '/partner/regenerate-credentials',
  },

  // 📊 Dashboard APIs
  DASHBOARD: {
    OVERVIEW: '/dashboard/overview',
    USAGE_CHART: '/dashboard/usage-chart',
    QUICK_STATS: '/dashboard/quick-stats',
  },

  // 💳 Bank Account Management
  BANK_ACCOUNTS: {
    LIST: '/bank-accounts',
    STATS: '/bank-accounts/stats',
    ADD: '/bank-accounts/add',
  },

  // 📋 Plan Management
  PLANS: {
    AVAILABLE: '/plans/available',
    USAGE: '/plans/usage',
    COMPARE: '/plans/compare',
    SUBSCRIBE: '/plans/subscribe',
    UPGRADE: '/plans/upgrade',
    SUBSCRIPTION_STATUS: '/plans/subscription-status',
  },

  // 💰 Billing
  BILLING: {
    LIST: '/bills',
    PAY: '/bills/pay',
  },

  // 📈 Analytics
  ANALYTICS: {
    OVERVIEW: '/analytics/overview',
    CALLS_OVER_TIME: '/analytics/calls-over-time',
    TOP_ENDPOINTS: '/analytics/top-endpoints',
    STATUS_CODES: '/analytics/status-codes',
    CALL_HISTORY: '/analytics/call-history',
  },

  // 🔧 System
  SYSTEM: {
    HEALTH: '/health',
  },

  // 💰 Billing
  BILLING: {
    LIST_BILLS: '/bills',
    PAY_BILL: '/bills/pay',
  },

  // 📈 Analytics
  ANALYTICS: {
    OVERVIEW: '/analytics/overview',
    CALLS_OVER_TIME: '/analytics/calls-over-time',
    TOP_ENDPOINTS: '/analytics/top-endpoints',
    STATUS_CODES: '/analytics/status-codes',
    CALL_HISTORY: '/analytics/call-history',
  },

  // 🔧 System
  SYSTEM: {
    HEALTH: '/health',
  },
};

// HTTP Status Codes
export const HTTP_STATUS = {
  OK: 200,
  CREATED: 201,
  NO_CONTENT: 204,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  CONFLICT: 409,
  UNPROCESSABLE_ENTITY: 422,
  INTERNAL_SERVER_ERROR: 500,
  BAD_GATEWAY: 502,
  SERVICE_UNAVAILABLE: 503,
};

// Request Headers
export const REQUEST_HEADERS = {
  CONTENT_TYPE: 'Content-Type',
  AUTHORIZATION: 'Authorization',
  ACCEPT: 'Accept',
  USER_AGENT: 'User-Agent',
};

// Content Types
export const CONTENT_TYPES = {
  JSON: 'application/json',
  FORM_DATA: 'multipart/form-data',
  URL_ENCODED: 'application/x-www-form-urlencoded',
  TEXT: 'text/plain',
};

// Time Ranges for Dashboard
export const TIME_RANGES = {
  LAST_15_MIN: 'last15min',
  LAST_HOUR: 'last1hour',
  LAST_4_HOURS: 'last4hours',
  LAST_12_HOURS: 'last12hours',
  LAST_24_HOURS: 'last24hours',
  LAST_7_DAYS: 'last7days',
  LAST_30_DAYS: 'last30days',
  LAST_90_DAYS: 'last90days',
  LAST_YEAR: 'lastyear',
};

export default {
  API_CONFIG,
  API_ENDPOINTS,
  HTTP_STATUS,
  REQUEST_HEADERS,
  CONTENT_TYPES,
  TIME_RANGES,
};
