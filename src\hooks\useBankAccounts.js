import { useState, useCallback } from 'react';
import { useApi } from './useApi';
import { API_ENDPOINTS } from '../constants/api';

export const useBankAccounts = () => {
  const { apiCall, loading, error } = useApi();
  const [bankAccounts, setBankAccounts] = useState([]);

  // Get list of bank accounts
  const getBankAccounts = useCallback(async () => {
    try {
      const response = await apiCall(API_ENDPOINTS.BANK_ACCOUNTS.LIST, {
        method: 'GET',
      });

      if (response.success && response.data) {
        setBankAccounts(response.data);
        return {
          success: true,
          data: response.data,
          message: response.message || 'Bank accounts loaded successfully'
        };
      } else {
        throw new Error(response.message || 'Failed to load bank accounts');
      }
    } catch (err) {
      return {
        success: false,
        message: err.message || 'Failed to load bank accounts'
      };
    }
  }, [apiCall]);

  // Get bank account statistics
  const getBankAccountStats = useCallback(async () => {
    try {
      const response = await apiCall(API_ENDPOINTS.BANK_ACCOUNTS.STATS, {
        method: 'GET',
      });

      if (response.success && response.data) {
        return {
          success: true,
          data: response.data,
          message: response.message || 'Bank account stats loaded successfully'
        };
      } else {
        throw new Error(response.message || 'Failed to load bank account stats');
      }
    } catch (err) {
      return {
        success: false,
        message: err.message || 'Failed to load bank account stats'
      };
    }
  }, [apiCall]);

  // Add new bank account
  const addBankAccount = useCallback(async (accountData) => {
    try {
      const response = await apiCall(API_ENDPOINTS.BANK_ACCOUNTS.ADD, {
        method: 'POST',
        data: accountData,
      });

      if (response.success && response.data) {
        // Refresh the bank accounts list
        await getBankAccounts();
        return {
          success: true,
          data: response.data,
          message: response.message || 'Bank account added successfully'
        };
      } else {
        throw new Error(response.message || 'Failed to add bank account');
      }
    } catch (err) {
      return {
        success: false,
        message: err.message || 'Failed to add bank account'
      };
    }
  }, [apiCall, getBankAccounts]);

  return {
    bankAccounts,
    getBankAccounts,
    getBankAccountStats,
    addBankAccount,
    loading,
    error
  };
};

export default useBankAccounts;
