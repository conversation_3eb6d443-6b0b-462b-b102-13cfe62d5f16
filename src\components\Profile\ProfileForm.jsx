import React from "react";
import { Icon } from "@iconify/react/dist/iconify.js";

const ProfileForm = ({
  profileData,
  plan,
  accounts,
  statusInfo,
  handleProfileChange,
  handleProfileSubmit,
  isLoading,
}) => {
  return (
    <div className="row g-4">
      {/* Thông tin chỉ đọc */}
      <div className="col-12 col-lg-4">
        <div className="card border border-neutral-200 h-100 profile-info-card">
          <div className="card-header">
            <h6 className="text-lg fw-semibold mb-0">Thông tin tài khoản</h6>
          </div>
          <div className="card-body">
            {/* Trạng thái */}
            <div className="d-flex align-items-center justify-content-between p-3 rounded-3 border border-success-200 bg-success-50 mb-3">
              <div className="d-flex align-items-center">
                <Icon
                  icon={statusInfo.icon}
                  className={`${statusInfo.class} me-2`}
                  width="20"
                />
                <span className="fw-medium text-neutral-700">Tr<PERSON>ng thái</span>
              </div>
              <span className={`fw-semibold ${statusInfo.class}`}>
                {statusInfo.text}
              </span>
            </div>
            {/* Username */}
            <div className="d-flex align-items-center p-3 rounded-3 border border-neutral-200 bg-neutral-50 mb-2">
              <div
                className="d-flex align-items-center justify-content-center me-3 rounded-2 border border-primary-200 bg-primary-50"
                style={{ width: "40px", height: "40px", minWidth: "40px" }}
              >
                <Icon
                  icon="solar:user-bold"
                  className="text-primary-600"
                  width="20"
                />
              </div>
              <div className="flex-grow-1">
                <div className="text-sm text-neutral-600 mb-0">Username</div>
                <div className="fw-medium text-neutral-900">
                  {profileData.username || "Chưa cập nhật"}
                </div>
              </div>
            </div>
            {/* Email */}
            <div className="d-flex align-items-center p-3 rounded-3 border border-neutral-200 bg-neutral-50 mb-2">
              <div
                className="d-flex align-items-center justify-content-center me-3 rounded-2 border border-primary-200 bg-primary-50"
                style={{ width: "40px", height: "40px", minWidth: "40px" }}
              >
                <Icon
                  icon="solar:letter-bold"
                  className="text-primary-600"
                  width="20"
                />
              </div>
              <div className="flex-grow-1">
                <div className="text-sm text-neutral-600 mb-0">Email</div>
                <div className="fw-medium text-neutral-900">
                  {profileData.email || "Chưa cập nhật"}
                </div>
              </div>
            </div>
            {/* Số điện thoại */}
            <div className="d-flex align-items-center p-3 rounded-3 border border-neutral-200 bg-neutral-50 mb-2">
              <div
                className="d-flex align-items-center justify-content-center me-3 rounded-2 border border-primary-200 bg-primary-50"
                style={{ width: "40px", height: "40px", minWidth: "40px" }}
              >
                <Icon
                  icon="solar:phone-bold"
                  className="text-primary-600"
                  width="20"
                />
              </div>
              <div className="flex-grow-1">
                <div className="text-sm text-neutral-600 mb-0">
                  Số điện thoại
                </div>
                <div className="fw-medium text-neutral-900">
                  {profileData.phone || "Chưa cập nhật"}
                </div>
              </div>
            </div>
            {/* Mã số thuế/CCCD */}
            <div className="d-flex align-items-center p-3 rounded-3 border border-neutral-200 bg-neutral-50">
              <div
                className="d-flex align-items-center justify-content-center me-3 rounded-2 border border-primary-200 bg-primary-50"
                style={{ width: "40px", height: "40px", minWidth: "40px" }}
              >
                <Icon
                  icon="solar:card-bold"
                  className="text-primary-600"
                  width="20"
                />
              </div>
              <div className="flex-grow-1">
                <div className="text-sm text-neutral-600 mb-0">
                  Mã số thuế/CCCD
                </div>
                <div className="fw-medium text-neutral-900">
                  {profileData.taxOrId || "Chưa cập nhật"}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      {/* Thông tin gói và tài khoản */}
      <div className="col-12 col-lg-4">
        <div className="card border border-neutral-200 h-100">
          <div className="card-header">
            <h6 className="text-lg fw-semibold mb-0">Thông tin gói</h6>
          </div>
          <div className="card-body">
            {/* Tên gói */}
            <div className="d-flex align-items-center p-3 rounded-3 border border-neutral-200 bg-neutral-50 mb-2">
              <div
                className="d-flex align-items-center justify-content-center me-3 rounded-2 border border-primary-200 bg-primary-50"
                style={{ width: "40px", height: "40px", minWidth: "40px" }}
              >
                <Icon
                  icon="solar:box-bold"
                  className="text-primary-600"
                  width="20"
                />
              </div>
              <div className="flex-grow-1">
                <div className="text-sm text-neutral-600 mb-0">Tên gói</div>
                <div className="fw-medium text-neutral-900">
                  {plan?.name || "Chưa đăng ký"}
                </div>
              </div>
            </div>
            {/* Giá gói */}
            <div className="d-flex align-items-center p-3 rounded-3 border border-neutral-200 bg-neutral-50 mb-2">
              <div
                className="d-flex align-items-center justify-content-center me-3 rounded-2 border border-primary-200 bg-primary-50"
                style={{ width: "40px", height: "40px", minWidth: "40px" }}
              >
                <Icon
                  icon="solar:wallet-bold"
                  className="text-primary-600"
                  width="20"
                />
              </div>
              <div className="flex-grow-1">
                <div className="text-sm text-neutral-600 mb-0">Giá gói</div>
                <div className="fw-medium text-neutral-900">
                  {plan?.base_price
                    ? plan.base_price.toLocaleString("vi-VN") + " đ"
                    : "0 đ"}
                </div>
              </div>
            </div>
            {/* Số tài khoản tối đa */}
            <div className="d-flex align-items-center p-3 rounded-3 border border-neutral-200 bg-neutral-50 mb-2">
              <div
                className="d-flex align-items-center justify-content-center me-3 rounded-2 border border-primary-200 bg-primary-50"
                style={{ width: "40px", height: "40px", minWidth: "40px" }}
              >
                <Icon
                  icon="solar:users-group-rounded-bold"
                  className="text-primary-600"
                  width="20"
                />
              </div>
              <div className="flex-grow-1">
                <div className="text-sm text-neutral-600 mb-0">
                  Số tài khoản tối đa
                </div>
                <div className="fw-medium text-neutral-900">
                  {plan?.account_limit || 0}
                </div>
              </div>
            </div>
            {/* Tài khoản đã tạo */}
            <div className="d-flex align-items-center p-3 rounded-3 border border-neutral-200 bg-neutral-50 mb-2">
              <div
                className="d-flex align-items-center justify-content-center me-3 rounded-2 border border-primary-200 bg-primary-50"
                style={{ width: "40px", height: "40px", minWidth: "40px" }}
              >
                <Icon
                  icon="solar:users-group-rounded-bold"
                  className="text-primary-600"
                  width="20"
                />
              </div>
              <div className="flex-grow-1">
                <div className="text-sm text-neutral-600 mb-0">
                  Tài khoản đã tạo
                </div>
                <div className="fw-medium text-neutral-900">
                  {accounts?.total || 0}
                </div>
              </div>
            </div>
            {/* Tài khoản đang hoạt động */}
            <div className="d-flex align-items-center p-3 rounded-3 border border-neutral-200 bg-neutral-50">
              <div
                className="d-flex align-items-center justify-content-center me-3 rounded-2 border border-primary-200 bg-primary-50"
                style={{ width: "40px", height: "40px", minWidth: "40px" }}
              >
                <Icon
                  icon="solar:check-circle-bold"
                  className="text-success-600"
                  width="20"
                />
              </div>
              <div className="flex-grow-1">
                <div className="text-sm text-neutral-600 mb-0">
                  Tài khoản đang hoạt động
                </div>
                <div className="fw-medium text-neutral-900">
                  {accounts?.active || 0}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      {/* Form có thể chỉnh sửa */}
      <div className="col-12 col-lg-4">
        <div className="card border border-neutral-200 h-100">
          <div className="card-header">
            <h6 className="text-lg fw-semibold mb-0">
              Cập nhật thông tin cá nhân
            </h6>
          </div>
          <div className="card-body">
            <form onSubmit={handleProfileSubmit}>
              <div className="row">
                <div className="col-md-6 mb-20">
                  <label className="form-label fw-semibold text-primary-light text-sm mb-8">
                    Họ <span className="text-danger-600">*</span>
                  </label>
                  <input
                    type="text"
                    name="firstName"
                    value={profileData.firstName}
                    onChange={handleProfileChange}
                    className="form-control radius-8"
                    placeholder="Nhập họ"
                    required
                  />
                </div>
                <div className="col-md-6 mb-20">
                  <label className="form-label fw-semibold text-primary-light text-sm mb-8">
                    Tên <span className="text-danger-600">*</span>
                  </label>
                  <input
                    type="text"
                    name="lastName"
                    value={profileData.lastName}
                    onChange={handleProfileChange}
                    className="form-control radius-8"
                    placeholder="Nhập tên"
                    required
                  />
                </div>
                <div className="col-12 mb-20">
                  <label className="form-label fw-semibold text-primary-light text-sm mb-8">
                    Địa chỉ
                  </label>
                  <input
                    type="text"
                    name="address"
                    value={profileData.address}
                    onChange={handleProfileChange}
                    className="form-control radius-8"
                    placeholder="Nhập địa chỉ"
                  />
                </div>
              </div>
              <div className="d-flex align-items-center justify-content-end gap-3">
                <button
                  type="submit"
                  disabled={isLoading}
                  className="btn btn-primary border border-primary-600 text-md px-56 py-12 radius-8"
                >
                  {isLoading ? (
                    <>
                      <span
                        className="spinner-border spinner-border-sm me-2"
                        role="status"
                        aria-hidden="true"
                      ></span>
                      Đang cập nhật...
                    </>
                  ) : (
                    "Cập nhật thông tin"
                  )}
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProfileForm;
