import React, { useState } from "react";
import { useAuth } from "../hooks/useAuth";
import { testAnalyticsEndpoints } from "../utils/apiTest";

const ApiTester = () => {
  const { user } = useAuth();
  const [testResults, setTestResults] = useState({});
  const [isLoading, setIsLoading] = useState(false);

  const runAnalyticsTest = async () => {
    if (!user?.token) {
      alert("Vui lòng đăng nhập trước khi test API");
      return;
    }

    setIsLoading(true);
    setTestResults({});

    try {
      console.log("🚀 Starting Analytics API tests...");

      // Test analytics endpoints only
      const analyticsResult = await testAnalyticsEndpoints(user.token);
      setTestResults({ analytics: analyticsResult });

      console.log("✅ Analytics API tests completed");
    } catch (error) {
      console.error("❌ Analytics API test error:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const getStatusIcon = (result) => {
    if (!result) return "⏳";
    return result.success ? "✅" : "❌";
  };

  const getStatusText = (result) => {
    if (!result) return "Chưa test";
    return result.success ? "Thành công" : `Lỗi: ${result.error}`;
  };

  return (
    <div className="card">
      <div className="card-header">
        <h5 className="card-title mb-0">Analytics API Tester</h5>
      </div>
      <div className="card-body">
        <div className="mb-3">
          <button
            className="btn btn-primary"
            onClick={runAnalyticsTest}
            disabled={isLoading || !user?.token}
          >
            {isLoading ? (
              <>
                <span
                  className="spinner-border spinner-border-sm me-2"
                  role="status"
                ></span>
                Đang test Analytics APIs...
              </>
            ) : (
              "Test Analytics APIs"
            )}
          </button>
        </div>

        {!user?.token && (
          <div className="alert alert-warning">
            Vui lòng đăng nhập để test API endpoints
          </div>
        )}

        <div className="row">
          <div className="col-12">
            <div className="card">
              <div className="card-header">
                <h6 className="mb-0">📈 Analytics APIs</h6>
              </div>
              <div className="card-body">
                <div className="d-flex align-items-center">
                  <span className="me-2">
                    {getStatusIcon(testResults.analytics)}
                  </span>
                  <span>{getStatusText(testResults.analytics)}</span>
                </div>
                <small className="text-muted">
                  Overview, Calls Over Time, Top Endpoints, Status Codes, Call
                  History
                </small>
              </div>
            </div>
          </div>
        </div>

        <div className="mt-3">
          <small className="text-muted">
            Mở Developer Console (F12) để xem chi tiết kết quả test API
          </small>
        </div>
      </div>
    </div>
  );
};

export default ApiTester;
