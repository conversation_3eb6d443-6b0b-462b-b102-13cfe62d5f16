import React, { Fragment } from "react";
import { Link, NavLink } from "react-router-dom";
import { Icon } from "@iconify/react/dist/iconify.js";
import { menuConfig } from "../MenuConfig";
import logo from "../../assets/images/logo.png";
import logoLight from "../../assets/images/logo-light.png";
import logoIcon from "../../assets/images/logo-icon.png";

const Sidebar = ({
  sidebarActive,
  mobileMenu,
  mobileMenuControl,
  openSidebarMenu,
  handleSidebarMenuToggle,
}) => (
  <aside
    className={
      sidebarActive
        ? "sidebar active"
        : mobileMenu
        ? "sidebar sidebar-open"
        : "sidebar"
    }
  >
    <button
      onClick={mobileMenuControl}
      className="sidebar-close-btn"
      type="button"
    >
      <Icon icon="radix-icons:cross-2" />
    </button>
    <div>
      <Link to="/dashboard" className="sidebar-logo">
        <img src={logo} alt="Pay2S logo" className="light-logo" />
        <img src={logoLight} alt="Pay2S logo light" className="dark-logo" />
        <img src={logoIcon} alt="Pay2S logo icon" className="logo-icon" />
      </Link>
    </div>
    <div className="sidebar-menu-area">
      <ul className="sidebar-menu" id="sidebar-menu">
        {menuConfig.map((group, i) => (
          <Fragment key={i}>
            {group.label && (
              <li className="sidebar-menu-group-title">{group.label}</li>
            )}
            {group.items.map((item, j) =>
              item.dropdown ? (
                <li
                  className={`dropdown ${
                    openSidebarMenu === item.label ? "open" : ""
                  }`}
                  key={j}
                >
                  <Link
                    to="#"
                    onClick={(e) => {
                      e.preventDefault();
                      handleSidebarMenuToggle(item.label);
                    }}
                  >
                    <Icon icon={item.icon} className="menu-icon" />
                    <span>{item.label}</span>
                  </Link>
                  <ul className="sidebar-submenu">
                    {item.dropdown.map((sub, k) => (
                      <li key={k}>
                        {sub.external && sub.url ? (
                          <a
                            href={sub.url}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="sidebar-external-link"
                          >
                            <i
                              className={`ri-circle-fill circle-icon text-${
                                sub.color || "success"
                              } w-auto`}
                            />
                            {sub.label}
                          </a>
                        ) : (
                          <NavLink
                            to={sub.to}
                            className={(nav) =>
                              nav.isActive ? "active-page" : ""
                            }
                          >
                            <i
                              className={`ri-circle-fill circle-icon text-${
                                sub.color || "success"
                              } w-auto`}
                            />
                            {sub.label}
                          </NavLink>
                        )}
                      </li>
                    ))}
                  </ul>
                </li>
              ) : (
                <li key={j}>
                  <NavLink
                    to={item.to}
                    className={(nav) => (nav.isActive ? "active-page" : "")}
                  >
                    <Icon icon={item.icon} className="menu-icon" />
                    <span>{item.label}</span>
                  </NavLink>
                </li>
              )
            )}
          </Fragment>
        ))}
      </ul>
    </div>
  </aside>
);

export default Sidebar;
