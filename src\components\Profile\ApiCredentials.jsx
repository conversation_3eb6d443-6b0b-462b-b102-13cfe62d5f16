import React, { useState } from "react";
import { Icon } from "@iconify/react/dist/iconify.js";
import { useProfile } from "../../hooks/useProfile";

const ApiCredentials = ({ profileData, setSuccessMessage }) => {
  const [showModal, setShowModal] = useState(false);
  const [newCredentials, setNewCredentials] = useState(null);
  const [showApiKey, setShowApiKey] = useState(false);
  const [copyAlert, setCopyAlert] = useState("");
  const [showConfirmPopup, setShowConfirmPopup] = useState(false);
  const { regenerateCredentials } = useProfile();

  const copyToClipboard = async (text, type) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopyAlert(`${type} đã được copy!`);
      setTimeout(() => setCopyAlert(""), 3000);
    } catch (err) {
      console.error("Failed to copy: ", err);
    }
  };

  const handleGetSecretKey = () => {
    setShowConfirmPopup(true);
  };

  const confirmGetSecretKey = async () => {
    setShowConfirmPopup(false);
    try {
      const result = await regenerateCredentials({
        regenerate_type: "api_secret",
      });
      if (result.success && result.new_credentials) {
        setNewCredentials(result.new_credentials);
        setShowModal(true);
        setSuccessMessage("Lấy Secret Key thành công!");
      } else {
        setSuccessMessage("Lấy Secret Key thất bại!");
      }
    } catch (error) {
      console.error("Error getting secret key:", error);
      setSuccessMessage("Có lỗi xảy ra khi lấy Secret Key!");
    }
  };

  const downloadCredentials = () => {
    if (!newCredentials) return;

    const content = `API Key: ${profileData.apiKey}\nSecret Key: ${newCredentials.api_secr}`;
    const blob = new Blob([content], { type: "text/plain" });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = "api-credentials.txt";
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);
  };

  const formatDate = (dateString) => {
    if (!dateString) return "N/A";
    return new Date(dateString).toLocaleString("vi-VN");
  };

  return (
    <>
      <div className="row">
        <div className="col-12">
          <div className="d-flex justify-content-between align-items-center mb-20">
            <h6 className="text-md fw-semibold mb-0">API Credentials</h6>
          </div>

          <div className="alert alert-info mb-20">
            <Icon icon="solar:info-circle-outline" className="me-2" />
            Bấm vào "Get Secret Key" để lấy Secret Key mới. Secret Key chỉ được
            cấp 1 lần duy nhất và không hiển thị lại. Nếu bạn mất Secret Key,
            bạn có thể tạo lại bằng cách bấm vào "Get Secret Key".
            <br />
            <strong>Lưu ý:</strong> Secret Key mới sẽ thay thế Secret Key cũ.
          </div>

          {copyAlert && (
            <div className="alert alert-success alert-dismissible fade show mb-20">
              <Icon icon="solar:check-circle-outline" className="me-2" />
              {copyAlert}
            </div>
          )}

          <div className="card border">
            <div className="card-body">
              <div className="row">
                <div className="col-md-6 mb-20">
                  <label className="form-label fw-semibold text-primary-light text-sm mb-8">
                    API Key
                  </label>
                  <div className="input-group">
                    <input
                      type={showApiKey ? "text" : "password"}
                      className="form-control radius-8"
                      value={
                        profileData?.apiKey ||
                        "••••••••••••••••••••••••••••••••"
                      }
                      readOnly
                    />
                    <button
                      className="btn btn-outline-secondary"
                      type="button"
                      onClick={() => setShowApiKey(!showApiKey)}
                    >
                      <Icon
                        icon={
                          showApiKey
                            ? "solar:eye-closed-outline"
                            : "solar:eye-outline"
                        }
                      />
                    </button>
                    <button
                      className="btn btn-outline-secondary"
                      type="button"
                      onClick={() =>
                        copyToClipboard(profileData?.apiKey, "API Key")
                      }
                      disabled={!profileData?.apiKey}
                    >
                      <Icon icon="solar:copy-outline" />
                    </button>
                    <button
                      type="button"
                      onClick={handleGetSecretKey}
                      className="btn btn-primary radius-8"
                    >
                      <Icon icon="solar:key-outline" className="me-2" />
                      Get Secret Key
                    </button>
                  </div>
                </div>
              </div>

              {profileData?.createdAt && (
                <div className="row">
                  <div className="col-12">
                    <small className="text-muted">
                      Tài khoản tạo lúc: {formatDate(profileData.createdAt)}
                    </small>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Credentials Modal */}
      {showModal && newCredentials && (
        <div
          className="modal fade show"
          style={{ display: "block" }}
          tabIndex="-1"
        >
          <div className="modal-dialog modal-dialog-centered">
            <div className="modal-content">
              <div className="modal-header">
                <h5 className="modal-title">
                  <Icon icon="solar:key-outline" className="me-2" />
                  API Credentials Mới
                </h5>
                <button
                  type="button"
                  className="btn-close"
                  onClick={() => setShowModal(false)}
                ></button>
              </div>
              <div className="modal-body">
                <div className="alert alert-warning">
                  <Icon icon="solar:danger-triangle-outline" className="me-2" />
                  <strong>Quan trọng:</strong> Đây là lần duy nhất bạn có thể
                  xem Secret Key. Hãy copy và lưu trữ an toàn.
                </div>

                <div className="mb-3">
                  <label className="form-label fw-semibold">API Key</label>
                  <div className="input-group">
                    <input
                      type="text"
                      className="form-control"
                      value={profileData?.apiKey || ""}
                      readOnly
                    />
                    <button
                      className="btn btn-outline-secondary"
                      type="button"
                      onClick={() =>
                        copyToClipboard(profileData?.apiKey, "API Key")
                      }
                    >
                      <Icon icon="solar:copy-outline" />
                    </button>
                  </div>
                </div>

                <div className="mb-3">
                  <label className="form-label fw-semibold">Secret Key</label>
                  <div className="input-group">
                    <input
                      type="text"
                      className="form-control"
                      value={newCredentials.api_secret || ""}
                      readOnly
                    />
                    <button
                      className="btn btn-outline-secondary"
                      type="button"
                      onClick={() =>
                        copyToClipboard(newCredentials.api_secret, "Secret Key")
                      }
                    >
                      <Icon icon="solar:copy-outline" />
                    </button>
                  </div>
                </div>
              </div>
              <div className="modal-footer">
                <button
                  type="button"
                  className="btn btn-secondary"
                  onClick={() => setShowModal(false)}
                >
                  Đóng
                </button>
                <button
                  type="button"
                  className="btn btn-success"
                  onClick={downloadCredentials}
                >
                  <Icon icon="solar:download-outline" className="me-2" />
                  Tải về File
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
      {showModal && <div className="modal-backdrop fade show"></div>}

      {/* Confirm Popup */}
      {showConfirmPopup && (
        <div className="modal fade show d-block" tabIndex="-1">
          <div className="modal-dialog modal-dialog-centered">
            <div className="modal-content">
              <div className="modal-header">
                <h5 className="modal-title">Xác nhận</h5>
                <button
                  type="button"
                  className="btn-close"
                  onClick={() => setShowConfirmPopup(false)}
                ></button>
              </div>
              <div className="modal-body">
                <p>
                  Bạn có chắc chắn muốn tạo Secret Key mới?
                  <br />
                  <strong>Lưu ý:</strong> Secret Key mới sẽ thay thế Secret Key
                  cũ và chỉ hiển thị một lần duy nhất.
                </p>
              </div>
              <div className="modal-footer">
                <button
                  type="button"
                  className="btn btn-secondary"
                  onClick={() => setShowConfirmPopup(false)}
                >
                  Hủy
                </button>
                <button
                  type="button"
                  className="btn btn-primary"
                  onClick={confirmGetSecretKey}
                >
                  Xác nhận
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
      {showConfirmPopup && <div className="modal-backdrop fade show"></div>}
    </>
  );
};

export default ApiCredentials;
