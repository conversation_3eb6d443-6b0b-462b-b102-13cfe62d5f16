import React from "react";

const StatisticsCards = ({ statisticsCards }) => (
  <>
    {statisticsCards.map((card, index) => (
      <div key={index} className="col-xxl-4 col-sm-6 my-3">
        <div className="card px-24 py-32 shadow-none radius-8 border h-100">
          <div className="card-body p-0 text-center">
            <h6 className="mb-8 fw-medium text-secondary-light text-sm">
              {card.title}
            </h6>
            <h2 className="fw-bold text-primary-light mb-8">{card.value}</h2>
            <p className="mb-0 text-xs text-secondary-light">{card.subtitle}</p>
          </div>
        </div>
      </div>
    ))}
  </>
);

export default StatisticsCards;
