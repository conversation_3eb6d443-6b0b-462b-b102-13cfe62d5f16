/* Custom styles for the application */

/* Notification Badge Styles */
.indicator-badge {
    position: absolute;
    top: calc(-1 * var(--size-8));
    right: calc(-1 * var(--size-8));
    min-width: var(--size-20);
    height: var(--size-20);
    padding: 0 var(--size-6);
    font-size: var(--font-xxs);
    font-weight: 600;
    line-height: var(--size-20);
    text-align: center;
    box-shadow: var(--shadow-4);
    z-index: 10;
}

.indicator-badge.bg-danger-main {
    background: linear-gradient(135deg, var(--danger-500) 0%, var(--danger-600) 100%);
    color: var(--base);
}

.indicator-badge.bg-success-main {
    background: linear-gradient(135deg, var(--success-500) 0%, var(--success-600) 100%);
    color: var(--base);
}

.indicator-badge.bg-warning-main {
    background: linear-gradient(135deg, var(--warning-500) 0%, var(--warning-600) 100%);
    color: var(--base);
}

.indicator-badge.bg-info-main {
    background: linear-gradient(135deg, var(--info-500) 0%, var(--info-600) 100%);
    color: var(--base);
}

/* Notification Button Hover Effect */
.has-indicator:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-5);
    transition: all 0.2s ease;
}

/* Notification Dropdown Animation */
.notification-dropdown .dropdown-menu {
    border: none;
    box-shadow: var(--shadow-6);
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(calc(-1 * var(--size-10)));
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}


.nav-pills .nav-link.active,
.nav-pills .show>.nav-link {
    background-color: var(--success-main);
}

.nav-pills .nav-link {
    color: var(--success-main);
}

/* Notification Dropdown Mobile Responsive */
@media (max-width: 768px) {
    .notification-dropdown .dropdown-menu {
        position: fixed !important;
        top: var(--size-80) !important;
        left: var(--size-16) !important;
        right: var(--size-16) !important;
        width: auto !important;
        max-width: none !important;
        transform: none !important;
        margin: 0 !important;
        border-radius: var(--rounded-12);
        max-height: 70vh;
        overflow-y: auto;
    }

    .notification-dropdown .dropdown-menu::before {
        display: none;
    }

    /* Notification items spacing on mobile */
    .notification-dropdown .dropdown-item {
        padding: var(--size-16) var(--size-20);
        border-bottom: 1px solid var(--border-color);
    }

    .notification-dropdown .dropdown-item:last-child {
        border-bottom: none;
    }

    /* Notification header on mobile */
    .notification-dropdown .dropdown-header {
        padding: var(--size-20);
        font-size: var(--font-lg);
        font-weight: 600;
        background-color: var(--neutral-50);
        border-bottom: 1px solid var(--border-color);
        position: sticky;
        top: 0;
        z-index: 10;
    }
}

/* Notification content improvements */
.notification-dropdown .notification-item {
    display: flex;
    align-items: flex-start;
    gap: var(--size-12);
    padding: var(--size-16);
    border-radius: var(--rounded-8);
    transition: background-color 0.2s ease;
}

.notification-dropdown .notification-item:hover {
    background-color: var(--neutral-50);
}

.notification-dropdown .notification-icon {
    flex-shrink: 0;
    width: var(--size-40);
    height: var(--size-40);
    border-radius: var(--rounded-8);
    display: flex;
    align-items: center;
    justify-content: center;
}

.notification-dropdown .notification-content {
    flex: 1;
    min-width: 0;
}

.notification-dropdown .notification-title {
    font-size: var(--font-sm);
    font-weight: 600;
    color: var(--text-primary-light);
    margin-bottom: var(--size-4);
    line-height: 1.4;
}

.notification-dropdown .notification-text {
    font-size: var(--font-xs);
    color: var(--text-secondary-light);
    line-height: 1.4;
    margin-bottom: var(--size-4);
}

.notification-dropdown .notification-time {
    font-size: var(--font-xxs);
    color: var(--text-secondary-light);
    opacity: 0.8;
}

.modal-title {
    font-size: 20px !important;
}