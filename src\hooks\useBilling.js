import { useState, useCallback } from 'react';
import { useApi } from './useApi';
import { API_ENDPOINTS } from '../constants/api';

export const useBilling = () => {
  const { apiCall, loading, error } = useApi();
  const [bills, setBills] = useState([]);

  // Get list of bills
  const getBills = useCallback(async (params = {}) => {
    try {
      const queryParams = new URLSearchParams(params).toString();
      const url = queryParams ? `${API_ENDPOINTS.BILLING.LIST_BILLS}?${queryParams}` : API_ENDPOINTS.BILLING.LIST_BILLS;
      
      const response = await apiCall(url, {
        method: 'GET',
      });

      if (response.success && response.data) {
        setBills(response.data);
        return {
          success: true,
          data: response.data,
          message: response.message || 'Bills loaded successfully'
        };
      } else {
        throw new Error(response.message || 'Failed to load bills');
      }
    } catch (err) {
      return {
        success: false,
        message: err.message || 'Failed to load bills'
      };
    }
  }, [apiCall]);

  // Pay a bill
  const payBill = useCallback(async (billId, paymentData) => {
    try {
      const response = await apiCall(API_ENDPOINTS.BILLING.PAY_BILL, {
        method: 'POST',
        data: { billId, ...paymentData },
      });

      if (response.success && response.data) {
        // Refresh bills list after payment
        await getBills();
        return {
          success: true,
          data: response.data,
          message: response.message || 'Bill paid successfully'
        };
      } else {
        throw new Error(response.message || 'Failed to pay bill');
      }
    } catch (err) {
      return {
        success: false,
        message: err.message || 'Failed to pay bill'
      };
    }
  }, [apiCall, getBills]);

  return {
    bills,
    getBills,
    payBill,
    loading,
    error
  };
};

export default useBilling;
