.jvm-zoom-btn.jvm-zoomin,
.jvm-zoom-btn.jvm-zoomout {
  top: 10px;
  background: #d1d5db;
  color: #111827;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 2px;
}

.slick-dots {
  display: flex !important;
}

.fc-toolbar-title {
  font-size: 24px !important;
  font-weight: 600;
}

.fc-button {
  border-color: var(--primary-600) !important;
  background: #fff !important;
  color: var(--primary-600) !important;
  font-weight: 600 !important;
  display: flex !important;
  justify-content: center !important;
  align-items: center !important;
  min-width: 80px !important;
}

.fc-button:hover {
  background-color: var(--primary-50) !important;
}

.fc-button-active {
  color: #fff !important;
  background-color: var(--primary-600) !important;
}

.fc-button-active:hover {
  color: #fff !important;
  background-color: var(--primary-600) !important;
}

.fc-button-active:focus,
.fc .fc-button-primary:not(:disabled):active:focus {
  box-shadow: none !important;
}

.fc-toolbar-chunk {
  display: flex !important;
}

.fc .fc-button-primary:focus {
  box-shadow: none !important;
}

.fc-h-event {
  border: 0;
  padding: 4px;
  border-radius: 4px;
  background-color: var(--primary-50) !important;
  color: var(--primary-600) !important;
}

.fc-h-event .fc-event-main {
  border: 0;
  padding: 4px;
  border-radius: 4px;
  background-color: var(--primary-50) !important;
  color: var(--primary-600) !important;
}

.fc-v-event .fc-event-main {
  border: 0;
  padding: 4px;
  border-radius: 4px;
  background-color: var(--primary-50) !important;
  color: var(--primary-600) !important;
}

.fc-daygrid-day-number {
  transition: background-color 0.2s;
  width: 30px;
  height: 30px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-weight: 600;
  border-radius: 50%;
}

.fc-daygrid-day:hover .fc-daygrid-day-number {
  background-color: var(--primary-100) !important;
  color: var(--primary-600) !important;
  transition: background-color 0.2s;
}

.slider-progress .active {
  transition: all 0.3s linear;
}

.slick-list {
  margin: 0 -7px;
}

.slick-slide>div {
  padding: 0 10px;
}

.center-gap {
  display: flex;
  align-items: center;
  gap: 6px;
}

.inline-grid {
  display: inline-grid;
}

.image-upload__boxInner.custom {
  display: flex;
  justify-content: center;
  align-items: center;
}

table.dataTable th.dt-type-numeric,
table.dataTable th.dt-type-date,
table.dataTable td.dt-type-numeric,
table.dataTable td.dt-type-date {
  text-align: unset !important;
}

/* Hide all submenus by default */
.sidebar-submenu {
  /* display: none; */
  list-style: none;
  padding-left: 20px;
  /* Adjust as needed */
}

.sidebar-menu .sidebar-submenu {
  display: block;
}

/* Optional: Add transition for smooth toggle */
.sidebar-menu .dropdown .sidebar-submenu {
  transition: max-height 0.3s linear;
  overflow: hidden;
  max-height: 0px;
}

/* .sidebar-menu .dropdown.open .sidebar-submenu {
  max-height: 1000px;
} */

.sidebar-menu li>a>i {
  margin-inline-end: 0;
}

.mr-10 {
  margin-right: 10px !important;
}

.sidebar-menu .sidebar-submenu li a {
  gap: 12px;
}

.sidebar-menu .sidebar-submenu li>.active-page {
  background-color: var(--button-secondary);
  color: var(--text-primary-light);
}

.sidebar-menu .sidebar-submenu li>.active-page:hover {
  color: black;
}

.sidebar-menu li>a.active-page:hover {
  color: white;
}

.me-8 {
  margin-right: 8px;
}

.gap-10 {
  display: flex;
  gap: 8px;
}

.w-33 {
  width: 33.33%;
}

.overlay::after {
  position: absolute;
  content: "";
  top: 0;
  inset-inline-start: 0;
  width: 0;
  height: 100%;
  background-color: #000;
  opacity: 0.65;
  transition: all 0.3s;
  z-index: 2;
}

.overlay.active::after {
  width: 100%;
}

.Gallery-masonry {
  background: #000;
  z-index: 99999;
  position: fixed;
  left: 0;
  top: 0;
  width: 100%;
  height: 100vh;
}

/* Profile Table Responsive Styles */
.profile-info-table {
  border-collapse: separate;
  border-spacing: 0;
}

.profile-info-table td {
  border: none !important;
  vertical-align: middle;
  padding: 12px 20px;
}

.profile-info-table tr:nth-child(odd) {
  background-color: #f8f9fa;
}

.profile-info-table tr:hover {
  background-color: #e9ecef;
  transition: background-color 0.2s ease;
}

.profile-info-label {
  font-weight: 600;
  color: #495057;
  white-space: nowrap;
  min-width: 140px;
}

.profile-info-value {
  color: #212529;
  word-break: break-word;
}

/* Mobile Responsive */
@media (max-width: 768px) {

  .profile-info-table,
  .profile-info-table tbody,
  .profile-info-table tr,
  .profile-info-table td {
    display: block;
    width: 100%;
  }

  .profile-info-table tr {
    border: 1px solid #dee2e6;
    border-radius: 8px;
    margin-bottom: 12px;
    padding: 0;
    background: white !important;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }

  .profile-info-table tr:hover {
    background: white !important;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  }

  .profile-info-table td {
    border: none !important;
    padding: 8px 16px;
    text-align: left;
  }

  .profile-info-table td:first-child {
    background: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
    font-weight: 600;
    color: #495057;
    padding-bottom: 8px;
  }

  .profile-info-table td:last-child {
    padding-top: 8px;
    color: #212529;
    font-weight: 500;
  }

  .profile-info-table .profile-info-label {
    min-width: auto;
  }
}

/* Extra small devices */
@media (max-width: 576px) {
  .profile-info-table td {
    padding: 6px 12px;
  }

  .profile-info-table td:first-child {
    padding-bottom: 6px;
  }

  .profile-info-table td:last-child {
    padding-top: 6px;
  }
}

/* Profile Header Responsive */
.profile-header {
  padding: 24px;
  border-bottom: 1px solid #dee2e6;
}

.profile-header-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.profile-header-info h5 {
  margin-bottom: 4px;
  font-size: 1.25rem;
  font-weight: 600;
}

.profile-header-info p {
  margin-bottom: 0;
  color: #6c757d;
  font-size: 0.875rem;
}

.profile-header-actions {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  align-items: center;
}

.profile-header-actions .btn {
  font-size: 0.875rem;
  padding: 8px 16px;
  border-radius: 6px;
  display: flex;
  align-items: center;
  gap: 6px;
  white-space: nowrap;
}

/* Mobile responsive for profile header */
@media (max-width: 768px) {
  .profile-header {
    padding: 16px;
  }

  .profile-header-content {
    gap: 12px;
  }

  .profile-header-info h5 {
    font-size: 1.125rem;
  }

  .profile-header-actions {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
  }

  .profile-header-actions .btn {
    justify-content: center;
    width: 100%;
  }
}

@media (max-width: 576px) {
  .profile-header {
    padding: 12px;
  }

  .profile-header-content {
    gap: 8px;
  }

  .profile-header-info h5 {
    font-size: 1rem;
  }

  .profile-header-info p {
    font-size: 0.8rem;
  }

  .profile-header-actions .btn {
    font-size: 0.8rem;
    padding: 6px 12px;
  }
}

/* Two column layout for larger screens */
@media (min-width: 992px) {
  .profile-header-content {
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    gap: 24px;
  }

  .profile-header-actions {
    flex-shrink: 0;
  }
}

/* Profile Info Card Layout */
.profile-info-card .card-body {
  padding: 20px;
}

.profile-info-card .row {
  margin: 0;
}

.profile-info-card .col-md-6,
.profile-info-card .col-12 {
  padding: 0 8px;
}

.profile-info-card .col-md-6:last-child,
.profile-info-card .col-12:last-child {
  margin-bottom: 0;
}

/* Info item styling */
.profile-info-item {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 12px;
  padding: 16px;
  transition: all 0.2s ease;
  height: 100%;
}

.profile-info-item:hover {
  background: #e9ecef;
  border-color: #dee2e6;
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.profile-info-item .text-sm {
  font-size: 0.75rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Status item special styling */
.profile-status-item {
  background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
  border: 1px solid #b8dacc;
}

.profile-status-item:hover {
  background: linear-gradient(135deg, #c3e6cb 0%, #b8dacc 100%);
}

/* Mobile responsive for card layout */
@media (max-width: 768px) {
  .profile-info-card .card-body {
    padding: 16px;
  }

  .profile-info-card .col-md-6,
  .profile-info-card .col-12 {
    padding: 0 4px;
    margin-bottom: 12px;
  }

  .profile-info-item {
    padding: 12px;
    border-radius: 8px;
  }

  .profile-info-item .text-sm {
    font-size: 0.7rem;
  }
}

@media (max-width: 576px) {
  .profile-info-card .card-body {
    padding: 12px;
  }

  .profile-info-card .col-md-6,
  .profile-info-card .col-12 {
    padding: 0 2px;
    margin-bottom: 8px;
  }

  .profile-info-item {
    padding: 10px;
    border-radius: 6px;
  }
}

/* Success Toast Notification */
.success-toast {
  animation: toastSlideInRight 0.3s ease-out;
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
  border: none;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(40, 167, 69, 0.3);
  backdrop-filter: blur(10px);
  min-width: 300px;
  max-width: 400px;
  padding: 16px 20px;
  font-weight: 500;
  letter-spacing: 0.3px;
  position: relative;
  overflow: hidden;
}

.success-toast::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #fff, rgba(255, 255, 255, 0.7), #fff);
  animation: shimmer 2s ease-in-out;
}

.success-toast .toast-content {
  display: flex;
  align-items: center;
  gap: 12px;
}

.success-toast .toast-icon {
  width: 24px;
  height: 24px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.success-toast .toast-text {
  flex: 1;
  font-size: 14px;
  line-height: 1.4;
}

@keyframes toastSlideInRight {
  0% {
    opacity: 0;
    transform: translateX(100%);
  }

  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }

  100% {
    transform: translateX(100%);
  }
}

/* Toast fade out animation */
.success-toast.fade-out {
  animation: toastSlideOutRight 0.3s ease-in forwards;
}

@keyframes toastSlideOutRight {
  0% {
    opacity: 1;
    transform: translateX(0);
  }

  100% {
    opacity: 0;
    transform: translateX(100%);
  }
}

/* Mobile responsive for toast */
@media (max-width: 576px) {
  .success-toast {
    min-width: 280px;
    max-width: 90vw;
    padding: 14px 16px;
    font-size: 13px;
  }

  .success-toast .toast-icon {
    width: 20px;
    height: 20px;
  }
}