import React from "react";
import { Icon } from "@iconify/react/dist/iconify.js";

const BarChart = ({ chartData }) => (
  <div className="col-xxl-8">
    <div className="card h-100 radius-8 border-0">
      <div className="card-body">
        <div className="d-flex align-items-center justify-content-between mb-20">
          <h6 className="mb-0 fw-bold text-lg">Group/Stacked Bar Chart</h6>
          <div className="d-flex align-items-center gap-2">
            <Icon
              icon="solar:menu-dots-outline"
              className="text-secondary-light"
            />
            <Icon
              icon="solar:maximize-outline"
              className="text-secondary-light"
            />
            <Icon
              icon="solar:menu-dots-bold"
              className="text-secondary-light"
            />
          </div>
        </div>
        <div className="d-flex align-items-center gap-3 mb-3">
          <div className="d-flex align-items-center gap-2">
            <input
              type="radio"
              id="grouped"
              name="chartType"
              defaultChecked
              className="form-check-input"
            />
            <label htmlFor="grouped" className="text-sm text-secondary-light">
              Grouped
            </label>
          </div>
          <div className="d-flex align-items-center gap-2">
            <input
              type="radio"
              id="stacked"
              name="chartType"
              className="form-check-input"
            />
            <label htmlFor="stacked" className="text-sm text-secondary-light">
              Stacked
            </label>
          </div>
        </div>
        <div className="position-relative" style={{ height: "300px" }}>
          <div
            className="position-absolute"
            style={{ left: "0", top: "0", height: "100%", width: "40px" }}
          >
            <div className="d-flex flex-column justify-content-between h-100 text-end pe-2">
              <small className="text-secondary-light">7.8</small>
              <small className="text-secondary-light">7.0</small>
              <small className="text-secondary-light">6.0</small>
              <small className="text-secondary-light">5.0</small>
              <small className="text-secondary-light">4.0</small>
              <small className="text-secondary-light">3.0</small>
              <small className="text-secondary-light">2.0</small>
              <small className="text-secondary-light">1.0</small>
              <small className="text-secondary-light">0.0</small>
            </div>
          </div>
          <div
            className="d-flex align-items-end justify-content-between h-100"
            style={{ marginLeft: "45px", paddingRight: "10px" }}
          >
            {chartData.map((data, index) => (
              <div
                key={index}
                className="d-flex align-items-end gap-1"
                style={{ height: "100%" }}
              >
                <div
                  className="bg-primary-600"
                  style={{
                    width: "3px",
                    height: `${(data.stream0 / 7.8) * 100}%`,
                    minHeight: "2px",
                  }}
                ></div>
                <div
                  className="bg-info-400"
                  style={{
                    width: "3px",
                    height: `${(data.stream1 / 7.8) * 100}%`,
                    minHeight: "2px",
                  }}
                ></div>
                <div
                  className="bg-warning-500"
                  style={{
                    width: "3px",
                    height: `${(data.stream2 / 7.8) * 100}%`,
                    minHeight: "2px",
                  }}
                ></div>
              </div>
            ))}
          </div>
          <div
            className="d-flex justify-content-between mt-2"
            style={{ marginLeft: "45px", paddingRight: "10px" }}
          >
            <small className="text-secondary-light">13</small>
            <small className="text-secondary-light">27</small>
            <small className="text-secondary-light">41</small>
            <small className="text-secondary-light">55</small>
          </div>
        </div>
        <div className="d-flex align-items-center gap-4 mt-3">
          <div className="d-flex align-items-center gap-2">
            <div className="w-12 h-12 bg-primary-600 rounded-circle"></div>
            <small className="text-secondary-light">Stream0</small>
          </div>
          <div className="d-flex align-items-center gap-2">
            <div className="w-12 h-12 bg-info-400 rounded-circle"></div>
            <small className="text-secondary-light">Stream1</small>
          </div>
          <div className="d-flex align-items-center gap-2">
            <div className="w-12 h-12 bg-warning-500 rounded-circle"></div>
            <small className="text-secondary-light">Stream2</small>
          </div>
        </div>
      </div>
    </div>
  </div>
);

export default BarChart;
