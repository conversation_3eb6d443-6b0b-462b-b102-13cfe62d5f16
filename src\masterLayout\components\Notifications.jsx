import React, { useState } from "react";
import { Icon } from "@iconify/react/dist/iconify.js";
import { Dropdown } from "bootstrap";

const Notifications = () => {
  const [notifications] = useState([
    {
      id: 1,
      title: "Ch<PERSON>o mừng bạn đến với hệ thống",
      message: "<PERSON><PERSON> thống đang hoạt động bình thường",
      time: "5 phút trước",
      type: "success",
      isRead: false,
    },
    {
      id: 2,
      title: "Cập nhật hệ thống",
      message: "Phiên bản mới đã được cài đặt thành công",
      time: "1 giờ trước",
      type: "info",
      isRead: false,
    },
    {
      id: 3,
      title: "Bảo trì định kỳ",
      message: "<PERSON><PERSON> thống sẽ bảo trì vào 2:00 AM ngày mai",
      time: "2 giờ trước",
      type: "warning",
      isRead: true,
    },
  ]);

  const unreadCount = notifications.filter((n) => !n.isRead).length;

  const getNotificationIcon = (type) => {
    switch (type) {
      case "success":
        return "ri-check-line";
      case "warning":
        return "ri-alert-line";
      case "info":
        return "ri-information-line";
      default:
        return "ri-notification-line";
    }
  };

  const getNotificationColor = (type) => {
    switch (type) {
      case "success":
        return "text-success";
      case "warning":
        return "text-warning";
      case "info":
        return "text-info";
      default:
        return "text-primary";
    }
  };
  return (
    <>
      {/* <button
        className="has-indicator w-40-px h-40-px bg-neutral-200 rounded-circle d-flex justify-content-center align-items-center"
        type="button"
        data-bs-toggle="dropdown"
        aria-expanded="false"
      >
        <Icon icon="iconoir:bell" className="text-primary-light text-xl" />
        {unreadCount > 0 && (
          <span className="indicator-badge bg-danger-main rounded-pill text-white position-absolute">
            {unreadCount}
          </span>
        )}
      </button> */}
      <div className="dropdown-menu to-top dropdown-menu-lg p-0">
        <div className="m-3 p-3 radius-8 bg-primary-50 mb-16 d-flex align-items-center justify-content-between gap-2">
          <div>
            <h6 className="text-lg text-primary-light fw-semibold mb-0">
              Thông báo
            </h6>
          </div>
          <span className="text-primary-600 fw-semibold text-lg w-40-px h-40-px rounded-circle bg-base d-flex justify-content-center align-items-center">
            {notifications.length}
          </span>
        </div>

        <div className="m-3">
          <div className="max-h-400-px overflow-y-auto scroll-sm pe-4">
            {notifications.map((notification) => (
              <div
                key={notification.id}
                className={`px-24 py-12 d-flex align-items-start gap-3 mb-2 justify-content-between radius-8 ${
                  !notification.isRead ? "bg-neutral-50" : ""
                }`}
              >
                <div className="text-black flex-grow-1 ">
                  <div className="d-flex align-items-center gap-2 mb-1">
                    <Icon
                      icon={getNotificationIcon(notification.type)}
                      className={`text-md ${getNotificationColor(
                        notification.type
                      )}`}
                    />
                    <h6 className="text-md fw-semibold mb-0">
                      {notification.title}
                    </h6>
                    {!notification.isRead && (
                      <span className="w-8 h-8 bg-info-main rounded-circle"></span>
                    )}
                  </div>
                  <p className="mb-1 text-secondary-light text-sm">
                    {notification.message}
                  </p>
                  <span className="text-xs text-secondary-light flex-shrink-0">
                    {notification.time}
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div>

        <div className="text-center py-12 px-16">
          <button className="text-primary-600 fw-semibold text-sm">
            Xem tất cả thông báo
          </button>
        </div>
      </div>
    </>
  );
};

export default Notifications;
