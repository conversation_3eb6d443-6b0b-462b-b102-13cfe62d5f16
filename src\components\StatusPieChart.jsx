import React from "react";
import { Icon } from "@iconify/react/dist/iconify.js";

const StatusPieChart = ({ statusCodeData }) => (
  <div className="col-xxl-4">
    <div className="card h-100 radius-8 border-0">
      <div className="card-body">
        <div className="d-flex align-items-center justify-content-between mb-20">
          <h6 className="mb-0 fw-bold text-lg">Status codes</h6>
          <div className="d-flex align-items-center gap-2">
            <Icon
              icon="solar:menu-dots-outline"
              className="text-secondary-light"
            />
            <Icon
              icon="solar:maximize-outline"
              className="text-secondary-light"
            />
            <Icon
              icon="solar:menu-dots-bold"
              className="text-secondary-light"
            />
          </div>
        </div>
        <div
          className="d-flex justify-content-center align-items-center position-relative"
          style={{ height: "200px" }}
        >
          <div className="position-relative">
            <svg width="160" height="160" className="position-absolute">
              <circle
                cx="80"
                cy="80"
                r="70"
                fill="none"
                stroke="var(--success-500)"
                strokeWidth="20"
                strokeDasharray="310 440"
                strokeDashoffset="0"
                transform="rotate(-90 80 80)"
              />
              <circle
                cx="80"
                cy="80"
                r="70"
                fill="none"
                stroke="var(--info-500)"
                strokeWidth="20"
                strokeDasharray="110 440"
                strokeDashoffset="-310"
                transform="rotate(-90 80 80)"
              />
              <circle
                cx="80"
                cy="80"
                r="70"
                fill="none"
                stroke="var(--danger-500)"
                strokeWidth="20"
                strokeDasharray="15 440"
                strokeDashoffset="-420"
                transform="rotate(-90 80 80)"
              />
              <circle
                cx="80"
                cy="80"
                r="70"
                fill="none"
                stroke="var(--warning-500)"
                strokeWidth="20"
                strokeDasharray="5 440"
                strokeDashoffset="-435"
                transform="rotate(-90 80 80)"
              />
            </svg>
            <div className="position-absolute top-50 start-50 translate-middle text-center">
              <h3 className="fw-bold text-primary-light mb-0">
                {statusCodeData.reduce(
                  (total, item) => total + (item.count || 0),
                  0
                )}
              </h3>
            </div>
          </div>
        </div>
        <div className="mt-3">
          {statusCodeData.map((item, index) => (
            <div
              key={index}
              className="d-flex align-items-center justify-content-between mb-2"
            >
              <div className="d-flex align-items-center gap-2">
                <div className={`w-12 h-12 bg-${item.color} rounded`}></div>
                <small className="text-secondary-light">{item.label}</small>
              </div>
              <small className="text-secondary-light">{item.percentage}</small>
            </div>
          ))}
          <div className="text-center mt-3">
            <div className="w-12 h-12 bg-neutral-800 rounded mx-auto mb-1"></div>
            <small className="text-secondary-light">200 Service Te...</small>
          </div>
        </div>
      </div>
    </div>
  </div>
);

export default StatusPieChart;
