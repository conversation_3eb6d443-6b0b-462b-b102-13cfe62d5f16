import React from "react";
import { Icon } from "@iconify/react/dist/iconify.js";

const ActivitiesTable = ({ activities, loading = false }) => {
  if (loading) {
    return <ActivitiesTableSkeleton />;
  }

  return (
    <div className="row">
      <div className="col-12">
        <h6 className="text-md fw-semibold mb-20">Hoạt động gần đây</h6>
        {activities.length > 0 ? (
          <div className="table-responsive">
            <table className="table table-striped">
              <thead>
                <tr>
                  <th>Thời gian</th>
                  <th>Hoạt động</th>
                  <th>IP Address</th>
                  <th>Trạng thái</th>
                </tr>
              </thead>
              <tbody>
                {activities.map((activity, index) => (
                  <tr key={index}>
                    <td>
                      {new Date(
                        activity.timestamp || activity.created_at
                      ).toLocaleString("vi-VN")}
                    </td>
                    <td>{activity.action || activity.activity}</td>
                    <td>{activity.ip_address || activity.ip || "N/A"}</td>
                    <td>
                      <span
                        className={`badge ${
                          activity.status === "success"
                            ? "bg-success"
                            : activity.status === "failed"
                            ? "bg-danger"
                            : "bg-secondary"
                        }`}
                      >
                        {activity.status || "Unknown"}
                      </span>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        ) : (
          <div className="text-center py-32">
            <Icon
              icon="solar:history-outline"
              className="text-secondary-light"
              style={{ fontSize: "48px" }}
            />
            <p className="text-secondary-light mt-16">Chưa có hoạt động nào</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default ActivitiesTable;
