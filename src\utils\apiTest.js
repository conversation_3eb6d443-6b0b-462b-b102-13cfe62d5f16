// API Testing Utilities - Updated for BankHub API
import axios from 'axios';
import { API_ENDPOINTS } from '../constants/api';

const BASE_URL = import.meta.env.DEV ? '/api' : 'https://api-partner.pay2s.vn';

// Create test API client
const testApiClient = axios.create({
  baseURL: BASE_URL,
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  },
});

// Test login API with real credentials
export const testLogin = async () => {
  try {
    const response = await testApiClient.post(API_ENDPOINTS.AUTH.LOGIN, {
      username: 'ng<PERSON><PERSON><PERSON>',
      password: 'NewPassword123!'
    });

    console.log('✅ Login response:', response.data);
    return response.data;
  } catch (error) {
    console.error('❌ Login error:', error.response?.data || error.message);
    throw error;
  }
};

// Test get current user info
export const testGetCurrentUser = async (token) => {
  try {
    const response = await testApiClient.get(API_ENDPOINTS.AUTH.CURRENT_USER, {
      headers: {
        'Authorization': `Bearer ${token}`,
      }
    });

    console.log('✅ Current user response:', response.data);
    return response.data;
  } catch (error) {
    console.error('❌ Get current user error:', error.response?.data || error.message);
    throw error;
  }
};

// Test get partner profile
export const testGetProfile = async (token) => {
  try {
    const response = await testApiClient.get(API_ENDPOINTS.PARTNER.PROFILE, {
      headers: {
        'Authorization': `Bearer ${token}`,
      }
    });

    console.log('✅ Profile response:', response.data);
    console.log('📋 Profile Data Structure:', JSON.stringify(response.data, null, 2));

    // Log specific fields for mapping
    if (response.data.success && response.data.profile) {
      const profile = response.data.profile;
      console.log('👤 Profile Fields:');
      console.log('- ID:', profile.id);
      console.log('- First Name:', profile.first_name);
      console.log('- Last Name:', profile.last_name);
      console.log('- Username:', profile.username);
      console.log('- Email:', profile.email);
      console.log('- Type:', profile.type);
      console.log('- Organization Name:', profile.organization_name);
      console.log('- Tax or ID:', profile.tax_or_id);
      console.log('- Address:', profile.address);
      console.log('- Status:', profile.status);
      console.log('- API Key:', profile.api_key);
      console.log('- Created At:', profile.created_at);
    }

    return response.data;
  } catch (error) {
    console.error('❌ Get profile error:', error.response?.data || error.message);
    throw error;
  }
};

// Test dashboard endpoints
export const testDashboardEndpoints = async (token) => {
  try {
    console.log('📊 Testing dashboard endpoints...');

    // Test dashboard overview
    const overviewResponse = await testApiClient.get(API_ENDPOINTS.DASHBOARD.OVERVIEW, {
      headers: { 'Authorization': `Bearer ${token}` }
    });
    console.log('✅ Dashboard overview:', overviewResponse.data);

    // Test usage chart
    const usageResponse = await testApiClient.get(`${API_ENDPOINTS.DASHBOARD.USAGE_CHART}?period=7`, {
      headers: { 'Authorization': `Bearer ${token}` }
    });
    console.log('✅ Usage chart data:', usageResponse.data);

    // Test quick stats
    const statsResponse = await testApiClient.get(API_ENDPOINTS.DASHBOARD.QUICK_STATS, {
      headers: { 'Authorization': `Bearer ${token}` }
    });
    console.log('✅ Quick stats:', statsResponse.data);

    return { success: true };
  } catch (error) {
    console.error('❌ Dashboard test error:', error.response?.data || error.message);
    return { success: false, error: error.message };
  }
};

// Test analytics endpoints
export const testAnalyticsEndpoints = async (token) => {
  try {
    console.log('📈 Testing analytics endpoints...');

    // Test analytics overview
    const overviewResponse = await testApiClient.get(`${API_ENDPOINTS.ANALYTICS.OVERVIEW}?timeRange=7`, {
      headers: { 'Authorization': `Bearer ${token}` }
    });
    console.log('✅ Analytics overview:', overviewResponse.data);

    // Test calls over time
    const callsResponse = await testApiClient.get(`${API_ENDPOINTS.ANALYTICS.CALLS_OVER_TIME}?timeRange=7`, {
      headers: { 'Authorization': `Bearer ${token}` }
    });
    console.log('✅ Calls over time:', callsResponse.data);

    // Test top endpoints
    const endpointsResponse = await testApiClient.get(`${API_ENDPOINTS.ANALYTICS.TOP_ENDPOINTS}?timeRange=7`, {
      headers: { 'Authorization': `Bearer ${token}` }
    });
    console.log('✅ Top endpoints:', endpointsResponse.data);

    // Test status codes
    const statusResponse = await testApiClient.get(`${API_ENDPOINTS.ANALYTICS.STATUS_CODES}?timeRange=7`, {
      headers: { 'Authorization': `Bearer ${token}` }
    });
    console.log('✅ Status codes:', statusResponse.data);

    return { success: true };
  } catch (error) {
    console.error('❌ Analytics test error:', error.response?.data || error.message);
    return { success: false, error: error.message };
  }
};

// Test bank accounts endpoints
export const testBankAccountsEndpoints = async (token) => {
  try {
    console.log('🏦 Testing bank accounts endpoints...');

    const accountsResponse = await testApiClient.get(API_ENDPOINTS.BANK_ACCOUNTS.LIST, {
      headers: { 'Authorization': `Bearer ${token}` }
    });
    console.log('✅ Bank accounts:', accountsResponse.data);

    const statsResponse = await testApiClient.get(API_ENDPOINTS.BANK_ACCOUNTS.STATS, {
      headers: { 'Authorization': `Bearer ${token}` }
    });
    console.log('✅ Bank account stats:', statsResponse.data);

    return { success: true };
  } catch (error) {
    console.error('❌ Bank accounts test error:', error.response?.data || error.message);
    return { success: false, error: error.message };
  }
};

// Run comprehensive API tests
export const runApiTests = async () => {
  try {
    console.log('🚀 Starting comprehensive API tests...');

    // Test login
    console.log('\n1. Testing login...');
    const loginResult = await testLogin();

    const token = loginResult.access_token || loginResult.token;
    if (token) {
      console.log('✅ Login successful!');

      // Test get current user
      console.log('\n2. Testing get current user...');
      await testGetCurrentUser(token);
      console.log('✅ Get current user successful!');

      // Test get profile
      console.log('\n3. Testing get profile...');
      await testGetProfile(token);
      console.log('✅ Get profile successful!');

      // Test dashboard endpoints
      console.log('\n4. Testing dashboard endpoints...');
      await testDashboardEndpoints(token);

      // Test bank accounts endpoints
      console.log('\n5. Testing bank accounts endpoints...');
      await testBankAccountsEndpoints(token);

    } else {
      console.log('❌ Login failed - no access token received');
    }

    console.log('\n🎉 All API tests completed!');
  } catch (error) {
    console.error('❌ API test failed:', error.message);
  }
};

// Test profile API specifically
export const testProfileAPI = async () => {
  console.log('🧪 Testing Profile API...');

  try {
    const token = localStorage.getItem('token');
    if (!token) {
      console.error('❌ No token found. Please login first.');
      return;
    }

    console.log('🔑 Using token:', token);

    const result = await testGetProfile(token);
    console.log('📊 Profile API Test Result:', result);

    return result;
  } catch (error) {
    console.error('❌ Profile API test failed:', error);
    throw error;
  }
};

// Make functions available globally for testing in browser console
if (typeof window !== 'undefined') {
  window.testLogin = testLogin;
  window.testGetCurrentUser = testGetCurrentUser;
  window.testGetProfile = testGetProfile;
  window.testProfileAPI = testProfileAPI;
  window.runApiTests = runApiTests;
}

export default {
  testLogin,
  testGetCurrentUser,
  testGetProfile,
  testDashboardEndpoints,
  testBankAccountsEndpoints,
  testProfileAPI,
  runApiTests
};
