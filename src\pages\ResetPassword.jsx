import React, { useState, useEffect } from "react";
import { Link, useSearchParams, useNavigate } from "react-router-dom";
import { Icon } from "@iconify/react/dist/iconify.js";
import { useApi } from "../hooks/useApi";
import { API_ENDPOINTS } from "../constants/api";

const ResetPassword = () => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const [formData, setFormData] = useState({
    new_password: "",
    confirmPassword: "",
  });
  const [loading, setLoading] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);
  const [errorMessage, setErrorMessage] = useState("");
  const [token, setToken] = useState("");
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const { apiCall } = useApi();

  useEffect(() => {
    const tokenFromUrl = searchParams.get("token");
    if (tokenFromUrl) {
      setToken(tokenFromUrl);
    } else {
      setErrorMessage("Token không hợp lệ hoặc đã hết hạn.");
    }
  }, [searchParams]);

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
    // Clear error when user starts typing
    if (errorMessage) {
      setErrorMessage("");
    }
  };

  const validatePassword = () => {
    if (formData.new_password.length < 6) {
      setErrorMessage("Mật khẩu phải có ít nhất 6 ký tự");
      return false;
    }
    if (formData.new_password !== formData.confirmPassword) {
      setErrorMessage("Mật khẩu xác nhận không khớp");
      return false;
    }
    return true;
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    if (!token) {
      setErrorMessage("Token không hợp lệ hoặc đã hết hạn.");
      return;
    }

    if (!validatePassword()) {
      return;
    }

    setLoading(true);
    setErrorMessage("");

    try {
      const response = await apiCall(API_ENDPOINTS.AUTH.RESET_PASSWORD, {
        method: "POST",
        data: {
          token: token,
          new_password: formData.new_password,
        },
      });

      if (response.success) {
        setIsSuccess(true);
      } else {
        setErrorMessage(response.message || "Đặt lại mật khẩu thất bại!");
      }
    } catch (error) {
      console.error("Reset password error:", error);
      setErrorMessage("Có lỗi xảy ra khi đặt lại mật khẩu. Vui lòng thử lại.");
    } finally {
      setLoading(false);
    }
  };

  const handleBackToLogin = () => {
    navigate("/login");
  };

  if (isSuccess) {
    return (
      <section className="auth bg-base d-flex flex-wrap">
        <div className="auth-left d-lg-block d-none">
          <div className="d-flex align-items-center flex-column h-100 justify-content-center">
            <img
              src="/assets/images/auth/auth-img.png"
              alt="Auth"
              className="img-fluid"
            />
          </div>
        </div>
        <div className="auth-right py-32 px-24 d-flex flex-column justify-content-center">
          <div className="max-w-464-px mx-auto w-100 text-center">
            <div className="mb-40">
              <div className="w-80-px h-80-px bg-success-100 rounded-circle d-flex align-items-center justify-content-center mx-auto mb-24">
                <Icon
                  icon="solar:check-circle-bold"
                  className="text-success-600 text-4xl"
                />
              </div>
              <h4 className="mb-12">Đặt lại mật khẩu thành công!</h4>
              <p className="mb-32 text-secondary-light text-lg">
                Mật khẩu của bạn đã được cập nhật thành công. Bạn có thể đăng
                nhập bằng mật khẩu mới.
              </p>
            </div>

            <div className="bg-neutral-50 radius-12 p-24 mb-32">
              <div className="d-flex align-items-center gap-12 mb-16">
                <Icon
                  icon="solar:shield-check-outline"
                  className="text-success-600 text-xl"
                />
                <h6 className="mb-0 text-success-600">Bảo mật tài khoản</h6>
              </div>
              <ul className="list-unstyled text-start text-secondary-light text-sm mb-0">
                <li className="mb-8">• Mật khẩu đã được mã hóa an toàn</li>
                <li className="mb-8">• Tài khoản của bạn đã được bảo vệ</li>
                <li className="mb-0">• Đăng nhập ngay để sử dụng dịch vụ</li>
              </ul>
            </div>

            <div className="d-flex flex-column gap-3">
              <button
                onClick={handleBackToLogin}
                className="btn btn-primary text-sm btn-sm px-12 py-16 w-100 radius-12"
              >
                <Icon icon="solar:login-outline" className="me-8" />
                Đăng nhập ngay
              </button>
            </div>

            <div className="mt-32 text-center text-sm">
              <p className="mb-0 text-secondary-light">
                Cần hỗ trợ?{" "}
                <Link to="/contact" className="text-primary-600 fw-semibold">
                  Liên hệ với chúng tôi
                </Link>
              </p>
            </div>
          </div>
        </div>
      </section>
    );
  }

  return (
    <section className="auth bg-base d-flex flex-wrap">
      <div className="auth-left d-lg-block d-none">
        <div className="d-flex align-items-center flex-column h-100 justify-content-center">
          <img
            src="/assets/images/auth/auth-img.png"
            alt="Auth"
            className="img-fluid"
          />
        </div>
      </div>
      <div className="auth-right py-32 px-24 d-flex flex-column justify-content-center">
        <div className="max-w-464-px mx-auto w-100">
          <div>
            <Link to="/" className="mb-40 max-w-290-px">
              <img
                src="/assets/images/logo.png"
                alt="Logo"
                className="img-fluid"
              />
            </Link>
            <h4 className="mb-12">Đặt lại mật khẩu</h4>
            <p className="mb-32 text-secondary-light text-lg">
              Nhập mật khẩu mới cho tài khoản của bạn
            </p>
          </div>

          {errorMessage && (
            <div
              className="alert alert-danger alert-dismissible fade show mb-24"
              role="alert"
            >
              <Icon icon="solar:danger-circle-outline" className="me-2" />
              {errorMessage}
              <button
                type="button"
                className="btn-close"
                onClick={() => setErrorMessage("")}
                aria-label="Close"
              ></button>
            </div>
          )}

          <form onSubmit={handleSubmit}>
            <div className="icon-field mb-24">
              <span className="icon top-50 translate-middle-y">
                <Icon icon="solar:lock-password-outline" />
              </span>
              <input
                type={showPassword ? "text" : "password"}
                name="new_password"
                value={formData.new_password}
                onChange={handleInputChange}
                className="form-control h-56-px bg-neutral-50 radius-12"
                placeholder="Nhập mật khẩu mới"
                required
                minLength={6}
              />
              <span
                className="toggle-password"
                onClick={() => setShowPassword(!showPassword)}
                style={{
                  position: "absolute",
                  right: "16px",
                  top: "50%",
                  transform: "translateY(-50%)",
                  cursor: "pointer",
                  zIndex: 5,
                }}
              >
                <Icon
                  icon={
                    showPassword
                      ? "solar:eye-outline"
                      : "solar:eye-closed-outline"
                  }
                  className="text-secondary-light"
                />
              </span>
            </div>

            <div className="icon-field mb-24">
              <span className="icon top-50 translate-middle-y">
                <Icon icon="solar:lock-password-outline" />
              </span>
              <input
                type={showConfirmPassword ? "text" : "password"}
                name="confirmPassword"
                value={formData.confirmPassword}
                onChange={handleInputChange}
                className="form-control h-56-px bg-neutral-50 radius-12"
                placeholder="Xác nhận mật khẩu mới"
                required
                minLength={6}
              />
              <span
                className="toggle-password"
                onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                style={{
                  position: "absolute",
                  right: "16px",
                  top: "50%",
                  transform: "translateY(-50%)",
                  cursor: "pointer",
                  zIndex: 5,
                }}
              >
                <Icon
                  icon={
                    showConfirmPassword
                      ? "solar:eye-outline"
                      : "solar:eye-closed-outline"
                  }
                  className="text-secondary-light"
                />
              </span>
            </div>

            <div className="bg-neutral-50 radius-12 p-16 mb-24">
              <div className="d-flex align-items-center gap-8 mb-12">
                <Icon
                  icon="solar:info-circle-outline"
                  className="text-primary-600 text-lg"
                />
                <span className="text-primary-600 fw-semibold text-sm">
                  Yêu cầu mật khẩu
                </span>
              </div>
              <ul className="list-unstyled text-secondary-light text-sm mb-0">
                <li className="mb-4">• Tối thiểu 6 ký tự</li>
                <li className="mb-4">
                  • Nên bao gồm chữ hoa, chữ thường và số
                </li>
                <li className="mb-0">• Tránh sử dụng thông tin cá nhân</li>
              </ul>
            </div>

            <button
              type="submit"
              className="btn btn-primary text-sm btn-sm px-12 py-16 w-100 radius-12 mb-24"
              disabled={loading || !token}
            >
              {loading ? (
                <>
                  <span
                    className="spinner-border spinner-border-sm me-2"
                    role="status"
                    aria-hidden="true"
                  ></span>
                  Đang cập nhật...
                </>
              ) : (
                <>
                  <Icon icon="solar:shield-check-outline" className="me-8" />
                  Cập nhật mật khẩu
                </>
              )}
            </button>
          </form>

          <div className="text-center">
            <Link
              to="/login"
              className="text-primary-600 fw-medium d-inline-flex align-items-center gap-2"
            >
              <Icon icon="solar:arrow-left-outline" />
              Quay lại đăng nhập
            </Link>
          </div>
        </div>
      </div>
    </section>
  );
};

export default ResetPassword;
