import { useState, useCallback } from 'react';
import { useApi } from './useApi';
import { API_ENDPOINTS } from '../constants/api';

export const useDashboard = () => {
  const { apiCall, loading, error } = useApi();
  const [dashboardData, setDashboardData] = useState(null);

  // Get dashboard overview with key metrics
  const getDashboardOverview = useCallback(async () => {
    try {
      const response = await apiCall(API_ENDPOINTS.DASHBOARD.OVERVIEW, {
        method: 'GET',
      });

      if (response.success && response.data) {
        setDashboardData(response.data);
        return {
          success: true,
          data: response.data,
          message: response.message || 'Dashboard overview loaded successfully'
        };
      } else {
        throw new Error(response.message || 'Failed to load dashboard overview');
      }
    } catch (err) {
      return {
        success: false,
        message: err.message || 'Failed to load dashboard overview'
      };
    }
  }, [apiCall]);

  // Get usage chart data for dashboard
  const getUsageChartData = useCallback(async (period = 7) => {
    try {
      const response = await apiCall(`${API_ENDPOINTS.DASHBOARD.USAGE_CHART}?period=${period}`, {
        method: 'GET',
      });

      if (response.success && response.data) {
        return {
          success: true,
          data: response.data,
          message: response.message || 'Usage chart data loaded successfully'
        };
      } else {
        throw new Error(response.message || 'Failed to load usage chart data');
      }
    } catch (err) {
      return {
        success: false,
        message: err.message || 'Failed to load usage chart data'
      };
    }
  }, [apiCall]);

  // Get quick statistics for dashboard widgets
  const getQuickStats = useCallback(async () => {
    try {
      const response = await apiCall(API_ENDPOINTS.DASHBOARD.QUICK_STATS, {
        method: 'GET',
      });

      if (response.success && response.data) {
        return {
          success: true,
          data: response.data,
          message: response.message || 'Quick stats loaded successfully'
        };
      } else {
        throw new Error(response.message || 'Failed to load quick stats');
      }
    } catch (err) {
      return {
        success: false,
        message: err.message || 'Failed to load quick stats'
      };
    }
  }, [apiCall]);

  return {
    dashboardData,
    getDashboardOverview,
    getUsageChartData,
    getQuickStats,
    loading,
    error
  };
};

export default useDashboard;
