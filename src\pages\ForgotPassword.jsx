import React, { useState } from "react";
import { <PERSON> } from "react-router-dom";
import { Icon } from "@iconify/react/dist/iconify.js";
import { useApi } from "../hooks/useApi";
import { API_ENDPOINTS } from "../constants/api";

const ForgotPassword = () => {
  const [formData, setFormData] = useState({
    email: "",
  });
  const [loading, setLoading] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [errorMessage, setErrorMessage] = useState("");
  const { apiCall } = useApi();

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setErrorMessage("");

    try {
      const response = await apiCall(API_ENDPOINTS.AUTH.FORGOT_PASSWORD, {
        method: "POST",
        data: {
          email: formData.email,
        },
      });

      if (response.success) {
        setIsSubmitted(true);
      } else {
        setErrorMessage(response.message || "Gửi email khôi phục thất bại!");
      }
    } catch (error) {
      console.error("Forgot password error:", error);
      setErrorMessage(
        "Có lỗi xảy ra khi gửi email khôi phục. Vui lòng thử lại."
      );
    } finally {
      setLoading(false);
    }
  };

  const handleResendEmail = async () => {
    setLoading(true);
    setErrorMessage("");

    try {
      const response = await apiCall(API_ENDPOINTS.AUTH.FORGOT_PASSWORD, {
        method: "POST",
        data: {
          email: formData.email,
        },
      });

      if (response.success) {
        alert("Email đã được gửi lại thành công!");
      } else {
        setErrorMessage(response.message || "Gửi lại email thất bại!");
      }
    } catch (error) {
      console.error("Resend email error:", error);
      setErrorMessage("Có lỗi xảy ra khi gửi lại email. Vui lòng thử lại.");
    } finally {
      setLoading(false);
    }
  };

  if (isSubmitted) {
    return (
      <section className="auth bg-base d-flex flex-wrap">
        <div className="auth-left d-lg-block d-none">
          <div className="d-flex align-items-center flex-column h-100 justify-content-center">
            <img
              src="/assets/images/auth/auth-img.png"
              alt="Auth"
              className="img-fluid"
            />
          </div>
        </div>
        <div className="auth-right py-32 px-24 d-flex flex-column justify-content-center">
          <div className="max-w-464-px mx-auto w-100 text-center">
            <div className="mb-40">
              <div className="w-80-px h-80-px bg-success-100 rounded-circle d-flex align-items-center justify-content-center mx-auto mb-24">
                <Icon
                  icon="solar:check-circle-bold"
                  className="text-success-600 text-4xl"
                />
              </div>
              <h4 className="mb-12">Email đã được gửi!</h4>
              <p className="mb-32 text-secondary-light text-lg">
                Chúng tôi đã gửi hướng dẫn khôi phục mật khẩu đến email{" "}
                <strong>{formData.email}</strong>
              </p>
            </div>

            <div className="bg-neutral-50 radius-12 p-24 mb-32">
              <div className="d-flex align-items-center gap-12 mb-16">
                <Icon
                  icon="solar:info-circle-outline"
                  className="text-primary-600 text-xl"
                />
                <h6 className="mb-0 text-primary-600">Lưu ý quan trọng</h6>
              </div>
              <ul className="list-unstyled text-start text-secondary-light text-sm mb-0">
                <li className="mb-8">• Kiểm tra cả hộp thư spam/junk mail</li>
                <li className="mb-8">
                  • Link khôi phục có hiệu lực trong 15 phút
                </li>
                <li className="mb-0">
                  • Nếu không nhận được email, hãy thử gửi lại
                </li>
              </ul>
            </div>

            <div className="d-flex flex-column gap-3">
              <button
                onClick={handleResendEmail}
                disabled={loading}
                className="btn btn-primary text-sm btn-sm px-12 py-16 w-100 radius-12"
              >
                {loading ? (
                  <>
                    <span
                      className="spinner-border spinner-border-sm me-2"
                      role="status"
                      aria-hidden="true"
                    ></span>
                    Đang gửi lại...
                  </>
                ) : (
                  <>
                    <Icon icon="solar:refresh-outline" className="me-8" />
                    Gửi lại email
                  </>
                )}
              </button>

              <Link
                to="/login"
                className="btn btn-primary text-sm btn-sm px-12 py-16 w-100 radius-12 text-decoration-none"
              >
                <Icon icon="solar:arrow-left-outline" className="me-8" />
                Quay lại đăng nhập
              </Link>
            </div>

            <div className="mt-32 text-center text-sm">
              <p className="mb-0 text-secondary-light">
                Vẫn gặp vấn đề?{" "}
                <Link to="/contact" className="text-primary-600 fw-semibold">
                  Liên hệ hỗ trợ
                </Link>
              </p>
            </div>
          </div>
        </div>
      </section>
    );
  }

  return (
    <section className="auth bg-base d-flex flex-wrap">
      <div className="auth-left d-lg-block d-none">
        <div className="d-flex align-items-center flex-column h-100 justify-content-center">
          <img
            src="/assets/images/auth/auth-img.png"
            alt="Auth"
            className="img-fluid"
          />
        </div>
      </div>
      <div className="auth-right py-32 px-24 d-flex flex-column justify-content-center">
        <div className="max-w-464-px mx-auto w-100">
          <div>
            <Link to="/" className="mb-40 max-w-290-px">
              <img
                src="/assets/images/logo.png"
                alt="Logo"
                className="img-fluid"
              />
            </Link>
            <h4 className="mb-12">Quên mật khẩu?</h4>
            <p className="mb-32 text-secondary-light text-lg">
              Không sao cả! Nhập email của bạn và chúng tôi sẽ gửi hướng dẫn
              khôi phục mật khẩu
            </p>
          </div>

          <form onSubmit={handleSubmit}>
            <div className="icon-field mb-24">
              <span className="icon top-50 translate-middle-y">
                <Icon icon="mage:email" />
              </span>
              <input
                type="email"
                name="email"
                value={formData.email}
                onChange={handleInputChange}
                className="form-control h-56-px bg-neutral-50 radius-12"
                placeholder="Nhập email của bạn"
                required
              />
            </div>

            <button
              type="submit"
              className="btn btn-primary text-sm btn-sm px-12 py-16 w-100 radius-12 mb-24"
              disabled={loading}
            >
              {loading ? (
                <>
                  <span
                    className="spinner-border spinner-border-sm me-2"
                    role="status"
                    aria-hidden="true"
                  ></span>
                  Đang gửi email...
                </>
              ) : (
                <>
                  <Icon icon="solar:letter-outline" className="me-8" />
                  Gửi hướng dẫn khôi phục
                </>
              )}
            </button>
          </form>

          <div className="text-center">
            <Link
              to="/login"
              className="text-primary-600 fw-medium d-inline-flex align-items-center gap-2"
            >
              <Icon icon="solar:arrow-left-outline" />
              Quay lại đăng nhập
            </Link>
          </div>
        </div>
      </div>
    </section>
  );
};

export default ForgotPassword;
