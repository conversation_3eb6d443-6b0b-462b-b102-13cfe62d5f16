import { useState, useCallback } from 'react';
import { useApi } from './useApi';
import { API_ENDPOINTS } from '../constants/api';

export const useAnalytics = () => {
  const { apiCall, loading, error } = useApi();
  const [analyticsData, setAnalyticsData] = useState(null);

  // Get analytics overview
  const getAnalyticsOverview = useCallback(async (params = {}) => {
    try {
      const queryParams = new URLSearchParams(params).toString();
      const url = queryParams ? `${API_ENDPOINTS.ANALYTICS.OVERVIEW}?${queryParams}` : API_ENDPOINTS.ANALYTICS.OVERVIEW;
      
      const response = await apiCall(url, {
        method: 'GET',
      });

      if (response.success && response.data) {
        setAnalyticsData(response.data);
        return {
          success: true,
          data: response.data,
          message: response.message || 'Analytics overview loaded successfully'
        };
      } else {
        throw new Error(response.message || 'Failed to load analytics overview');
      }
    } catch (err) {
      return {
        success: false,
        message: err.message || 'Failed to load analytics overview'
      };
    }
  }, [apiCall]);

  // Get API calls over time
  const getCallsOverTime = useCallback(async (params = {}) => {
    try {
      const queryParams = new URLSearchParams(params).toString();
      const url = queryParams ? `${API_ENDPOINTS.ANALYTICS.CALLS_OVER_TIME}?${queryParams}` : API_ENDPOINTS.ANALYTICS.CALLS_OVER_TIME;
      
      const response = await apiCall(url, {
        method: 'GET',
      });

      if (response.success && response.data) {
        return {
          success: true,
          data: response.data,
          message: response.message || 'Calls over time data loaded successfully'
        };
      } else {
        throw new Error(response.message || 'Failed to load calls over time data');
      }
    } catch (err) {
      return {
        success: false,
        message: err.message || 'Failed to load calls over time data'
      };
    }
  }, [apiCall]);

  // Get top endpoints
  const getTopEndpoints = useCallback(async (params = {}) => {
    try {
      const queryParams = new URLSearchParams(params).toString();
      const url = queryParams ? `${API_ENDPOINTS.ANALYTICS.TOP_ENDPOINTS}?${queryParams}` : API_ENDPOINTS.ANALYTICS.TOP_ENDPOINTS;
      
      const response = await apiCall(url, {
        method: 'GET',
      });

      if (response.success && response.data) {
        return {
          success: true,
          data: response.data,
          message: response.message || 'Top endpoints data loaded successfully'
        };
      } else {
        throw new Error(response.message || 'Failed to load top endpoints data');
      }
    } catch (err) {
      return {
        success: false,
        message: err.message || 'Failed to load top endpoints data'
      };
    }
  }, [apiCall]);

  // Get status codes distribution
  const getStatusCodes = useCallback(async (params = {}) => {
    try {
      const queryParams = new URLSearchParams(params).toString();
      const url = queryParams ? `${API_ENDPOINTS.ANALYTICS.STATUS_CODES}?${queryParams}` : API_ENDPOINTS.ANALYTICS.STATUS_CODES;
      
      const response = await apiCall(url, {
        method: 'GET',
      });

      if (response.success && response.data) {
        return {
          success: true,
          data: response.data,
          message: response.message || 'Status codes data loaded successfully'
        };
      } else {
        throw new Error(response.message || 'Failed to load status codes data');
      }
    } catch (err) {
      return {
        success: false,
        message: err.message || 'Failed to load status codes data'
      };
    }
  }, [apiCall]);

  // Get call history
  const getCallHistory = useCallback(async (params = {}) => {
    try {
      const queryParams = new URLSearchParams(params).toString();
      const url = queryParams ? `${API_ENDPOINTS.ANALYTICS.CALL_HISTORY}?${queryParams}` : API_ENDPOINTS.ANALYTICS.CALL_HISTORY;
      
      const response = await apiCall(url, {
        method: 'GET',
      });

      if (response.success && response.data) {
        return {
          success: true,
          data: response.data,
          message: response.message || 'Call history loaded successfully'
        };
      } else {
        throw new Error(response.message || 'Failed to load call history');
      }
    } catch (err) {
      return {
        success: false,
        message: err.message || 'Failed to load call history'
      };
    }
  }, [apiCall]);

  return {
    analyticsData,
    getAnalyticsOverview,
    getCallsOverTime,
    getTopEndpoints,
    getStatusCodes,
    getCallHistory,
    loading,
    error
  };
};

export default useAnalytics;
