import { useState, useCallback } from 'react';
import { useApi } from './useApi';
import { API_ENDPOINTS } from '../constants/api';

export const useProfile = () => {
  const { apiCall, loading, error } = useApi();
  const [profile, setProfile] = useState(null);

  // Get user profile
  const getProfile = useCallback(async () => {
    try {
      const response = await apiCall(API_ENDPOINTS.PARTNER.PROFILE, {
        method: 'GET',
      });

      if (response && response.success && response.profile) {
        setProfile(response.profile);
        return {
          success: true,
          profile: response.profile,
          message: 'Profile loaded successfully'
        };
      } else if (response) {
        // Handle case where response doesn't have expected structure
        setProfile(response);
        return {
          success: true,
          profile: response,
          message: 'Profile loaded successfully'
        };
      } else {
        throw new Error('Failed to load profile');
      }
    } catch (err) {
      return {
        success: false,
        message: err.message || 'Failed to load profile'
      };
    }
  }, [apiCall]);

  // Update user profile
  const updateProfile = useCallback(async (profileData) => {
    try {
      const response = await apiCall(API_ENDPOINTS.PARTNER.UPDATE_PROFILE, {
        method: 'PUT',
        data: profileData,
      });

      if (response) {
        setProfile(response);
        // Update localStorage if needed
        const userData = localStorage.getItem('userData');
        if (userData) {
          const parsedUser = JSON.parse(userData);
          const updatedUser = { ...parsedUser, ...response };
          localStorage.setItem('userData', JSON.stringify(updatedUser));
        }

        return {
          success: true,
          data: response,
          message: 'Profile updated successfully'
        };
      } else {
        throw new Error('Failed to update profile');
      }
    } catch (err) {
      return {
        success: false,
        message: err.message || 'Failed to update profile'
      };
    }
  }, [apiCall]);

  // Change password
  const changePassword = useCallback(async (passwordData) => {
    try {
      const response = await apiCall(API_ENDPOINTS.PARTNER.CHANGE_PASSWORD, {
        method: 'PUT',
        data: {
          current_password: passwordData.currentPassword,
          new_password: passwordData.newPassword,
        },
      });

      if (response) {
        return {
          success: true,
          message: 'Password changed successfully'
        };
      } else {
        throw new Error('Failed to change password');
      }
    } catch (err) {
      return {
        success: false,
        message: err.message || 'Failed to change password'
      };
    }
  }, [apiCall]);

  // Get partner activities
  const getActivities = useCallback(async (params = {}) => {
    try {
      const queryParams = new URLSearchParams(params).toString();
      const url = queryParams ? `${API_ENDPOINTS.PARTNER.ACTIVITIES}?${queryParams}` : API_ENDPOINTS.PARTNER.ACTIVITIES;

      const response = await apiCall(url, {
        method: 'GET',
      });

      if (response.success && response.data) {
        return {
          success: true,
          data: response.data,
          message: response.message || 'Activities loaded successfully'
        };
      } else {
        throw new Error(response.message || 'Failed to load activities');
      }
    } catch (err) {
      return {
        success: false,
        message: err.message || 'Failed to load activities'
      };
    }
  }, [apiCall]);

  // Regenerate API credentials
  const regenerateCredentials = useCallback(async (data) => {
    try {
      const response = await apiCall(API_ENDPOINTS.PARTNER.REGENERATE_CREDENTIALS, {
        method: 'POST',
        data: data
      });

      if (response.success && response.new_credentials) {
        return {
          success: true,
          new_credentials: response.new_credentials,
          message: response.message || 'API credentials regenerated successfully'
        };
      } else {
        throw new Error(response.message || 'Failed to regenerate credentials');
      }
    } catch (err) {
      return {
        success: false,
        message: err.message || 'Failed to regenerate credentials'
      };
    }
  }, [apiCall]);

  return {
    profile,
    getProfile,
    updateProfile,
    changePassword,
    getActivities,
    regenerateCredentials,
    loading,
    error
  };
};

export default useProfile;
