import React from "react";
import { Icon } from "@iconify/react/dist/iconify.js";

const PasswordForm = ({
  passwordData,
  showPasswords,
  handlePasswordChange,
  handlePasswordSubmit,
  togglePasswordVisibility,
  isLoading,
}) => (
  <form onSubmit={handlePasswordSubmit}>
    <div className="row">
      <div className="col-12 mb-20">
        <label className="form-label fw-semibold text-primary-light text-sm mb-8">
          Mật khẩu hiện tại <span className="text-danger-600">*</span>
        </label>
        <div className="position-relative">
          <input
            type={showPasswords.current ? "text" : "password"}
            name="currentPassword"
            value={passwordData.current_password}
            onChange={handlePasswordChange}
            className="form-control radius-8"
            placeholder="Nhập mật khẩu hiện tại"
            required
          />
          <span
            className="position-absolute end-0 top-50 translate-middle-y me-16 cursor-pointer"
            onClick={() => togglePasswordVisibility("current")}
          >
            <Icon
              icon={
                showPasswords.current
                  ? "solar:eye-bold"
                  : "solar:eye-closed-linear"
              }
            />
          </span>
        </div>
      </div>
      <div className="col-12 mb-20">
        <label className="form-label fw-semibold text-primary-light text-sm mb-8">
          Mật khẩu mới <span className="text-danger-600">*</span>
        </label>
        <div className="position-relative">
          <input
            type={showPasswords.new ? "text" : "password"}
            name="newPassword"
            value={passwordData.new_password}
            onChange={handlePasswordChange}
            className="form-control radius-8"
            placeholder="Nhập mật khẩu mới"
            required
          />
          <span
            className="position-absolute end-0 top-50 translate-middle-y me-16 cursor-pointer"
            onClick={() => togglePasswordVisibility("new")}
          >
            <Icon
              icon={
                showPasswords.new ? "solar:eye-bold" : "solar:eye-closed-linear"
              }
            />
          </span>
        </div>
      </div>
      <div className="col-12 mb-20">
        <label className="form-label fw-semibold text-primary-light text-sm mb-8">
          Xác nhận mật khẩu mới <span className="text-danger-600">*</span>
        </label>
        <div className="position-relative">
          <input
            type={showPasswords.confirm ? "text" : "password"}
            name="confirmPassword"
            value={passwordData.confirmPassword}
            onChange={handlePasswordChange}
            className="form-control radius-8"
            placeholder="Nhập lại mật khẩu mới"
            required
          />
          <span
            className="position-absolute end-0 top-50 translate-middle-y me-16 cursor-pointer"
            onClick={() => togglePasswordVisibility("confirm")}
          >
            <Icon
              icon={
                showPasswords.confirm
                  ? "solar:eye-bold"
                  : "solar:eye-closed-linear"
              }
            />
          </span>
        </div>
      </div>
    </div>
    <div className="d-flex align-items-center justify-content-end gap-3">
      <button
        type="submit"
        disabled={isLoading}
        className="btn btn-primary border border-primary-600 text-md px-56 py-12 radius-8"
      >
        {isLoading ? (
          <>
            <span
              className="spinner-border spinner-border-sm me-2"
              role="status"
              aria-hidden="true"
            ></span>
            Đang đổi...
          </>
        ) : (
          "Đổi mật khẩu"
        )}
      </button>
    </div>
  </form>
);

export default PasswordForm;
