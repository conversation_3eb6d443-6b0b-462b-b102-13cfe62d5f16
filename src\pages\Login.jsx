import React, { useState } from "react";
import { Link, useNavigate } from "react-router-dom";
import { Icon } from "@iconify/react/dist/iconify.js";
import { useAuth } from "../hooks/useAuth";

const Login = () => {
  const [formData, setFormData] = useState({
    username: "",
    password: "",
    rememberMe: false,
  });
  const [showPassword, setShowPassword] = useState(false);
  const [errorMessage, setErrorMessage] = useState("");
  const navigate = useNavigate();
  const { login, loading, error } = useAuth();

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: type === "checkbox" ? checked : value,
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setErrorMessage(""); // Clear previous errors

    try {
      const credentials = {
        username: formData.username,
        password: formData.password,
      };

      console.log("Attempting login with:", credentials);
      const result = await login(credentials);
      console.log("Login result:", result);

      if (result.success) {
        // Store additional data if needed
        if (formData.rememberMe) {
          localStorage.setItem("rememberMe", "true");
        }

        console.log("Login successful, navigating to dashboard...");
        // Navigate to dashboard
        navigate("/dashboard");
      } else {
        // Handle login error
        console.error("Login failed:", result.message);
        setErrorMessage(
          result.message ||
            "Đăng nhập thất bại. Vui lòng kiểm tra thông tin đăng nhập."
        );
      }
    } catch (err) {
      console.error("Login error:", err);
      setErrorMessage(
        "Có lỗi xảy ra trong quá trình đăng nhập. Vui lòng thử lại."
      );
    }
  };

  return (
    <section className="auth bg-base d-flex flex-wrap">
      <div className="auth-left d-lg-block d-none">
        <div className="d-flex align-items-center flex-column h-100 justify-content-center">
          <img
            src="/assets/images/auth/auth-img.png"
            alt="Auth"
            className="img-fluid"
          />
        </div>
      </div>
      <div className="auth-right py-32 px-24 d-flex flex-column justify-content-center">
        <div className="max-w-464-px mx-auto w-100">
          <div>
            <Link to="/" className="mb-40 max-w-290-px">
              <img
                src="/assets/images/logo.png"
                alt="Logo"
                className="img-fluid"
              />
            </Link>
            <h4 className="mb-12">Đăng nhập vào tài khoản</h4>
            <p className="mb-32 text-secondary-light text-lg">
              Chào mừng bạn trở lại! Vui lòng nhập thông tin đăng nhập
            </p>
          </div>
          <form onSubmit={handleSubmit}>
            {errorMessage && (
              <div className="alert alert-danger mb-16" role="alert">
                <Icon icon="solar:danger-circle-outline" className="me-2" />
                {errorMessage}
              </div>
            )}

            <div className="icon-field mb-16">
              <span className="icon top-50 translate-middle-y">
                <Icon icon="mage:user" />
              </span>
              <input
                type="text"
                name="username"
                value={formData.username}
                onChange={handleInputChange}
                className="form-control h-56-px bg-neutral-50 radius-12"
                placeholder="Nhập username của bạn"
                required
                disabled={loading}
              />
            </div>
            <div className="position-relative mb-20">
              <div className="icon-field">
                <span className="icon top-50 translate-middle-y">
                  <Icon icon="solar:lock-password-outline" />
                </span>
                <input
                  type={showPassword ? "text" : "password"}
                  name="password"
                  value={formData.password}
                  onChange={handleInputChange}
                  className="form-control h-56-px bg-neutral-50 radius-12"
                  placeholder="Nhập mật khẩu"
                  required
                  disabled={loading}
                />
              </div>
              <span
                className="toggle-password cursor-pointer position-absolute end-0 top-50 translate-middle-y me-16 text-secondary-light"
                onClick={() => setShowPassword(!showPassword)}
              >
                <Icon
                  icon={
                    showPassword ? "solar:eye-bold" : "solar:eye-closed-linear"
                  }
                />
              </span>
            </div>
            <div className="d-flex align-items-center justify-content-between mb-32">
              <div className="form-check style-check d-flex align-items-center">
                <input
                  className="form-check-input border border-neutral-300"
                  type="checkbox"
                  name="rememberMe"
                  checked={formData.rememberMe}
                  onChange={handleInputChange}
                  id="rememberMe"
                />
                <label className="form-check-label" htmlFor="rememberMe">
                  Ghi nhớ đăng nhập
                </label>
              </div>
              <Link
                to="/forgot-password"
                className="text-primary-600 fw-medium"
              >
                Quên mật khẩu?
              </Link>
            </div>
            <button
              type="submit"
              className="btn btn-primary text-sm btn-sm px-12 py-16 w-100 radius-12"
              disabled={loading}
            >
              {loading ? (
                <>
                  <span
                    className="spinner-border spinner-border-sm me-2"
                    role="status"
                    aria-hidden="true"
                  ></span>
                  Đang đăng nhập...
                </>
              ) : (
                "Đăng nhập"
              )}
            </button>
          </form>
          <div className="mt-32 text-center text-sm">
            <p className="mb-0">
              Chưa có tài khoản?{" "}
              <Link to="#" className="text-primary-600 fw-semibold">
                Liên hệ hợp tác
              </Link>
            </p>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Login;
