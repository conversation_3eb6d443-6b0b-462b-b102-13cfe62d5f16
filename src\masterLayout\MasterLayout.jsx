import React, { useEffect, useState } from "react";
import { useLocation } from "react-router-dom";
import { menuConfig } from "./MenuConfig";
import Sidebar from "./components/Sidebar";
import Navbar from "./components/Navbar";

const MasterLayout = ({ children }) => {
  const [sidebarActive, setSidebarActive] = useState(false);
  const [mobileMenu, setMobileMenu] = useState(false);
  const location = useLocation();

  const [openSidebarMenu, setOpenSidebarMenu] = useState(null);

  const handleSidebarMenuToggle = (menuLabel) => {
    setOpenSidebarMenu((prevMenu) =>
      prevMenu === menuLabel ? null : menuLabel
    );
  };

  useEffect(() => {
    let activeParentLabel = null;
    menuConfig.forEach((group) => {
      group.items.forEach((item) => {
        if (
          item.dropdown &&
          item.dropdown.some((subItem) => subItem.to === location.pathname)
        ) {
          activeParentLabel = item.label;
        }
      });
    });
    setOpenSidebarMenu(activeParentLabel);
  }, [location.pathname]);

  const sidebarControl = () => setSidebarActive((prev) => !prev);
  const mobileMenuControl = () => setMobileMenu((prev) => !prev);

  return (
    <section className={mobileMenu ? "overlay active" : "overlay"}>
      {/* Sidebar navigation */}
      <Sidebar
        sidebarActive={sidebarActive}
        mobileMenu={mobileMenu}
        mobileMenuControl={mobileMenuControl}
        openSidebarMenu={openSidebarMenu}
        handleSidebarMenuToggle={handleSidebarMenuToggle}
      />
      <main
        className={sidebarActive ? "dashboard-main active" : "dashboard-main"}
      >
        {/* Navbar */}
        <Navbar
          sidebarActive={sidebarActive}
          sidebarToggle={sidebarControl}
          mobileMenuToggle={mobileMenuControl}
        />
        <div className="dashboard-main-body">{children}</div>
        <footer className="d-footer">
          <div className="row align-items-center justify-content-between">
            <div className="col-auto">
              <p className="mb-0">
                © 2025 - <span className="text-primary-600">Pay2S</span> - API
                ngân hàng và Ví điện tử tốc độ nhanh, bảo mật, giao dịch không
                giới hạn
              </p>
            </div>
          </div>
        </footer>
      </main>
    </section>
  );
};

export default MasterLayout;
