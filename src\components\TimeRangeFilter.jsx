import React from "react";
import { Icon } from "@iconify/react/dist/iconify.js";

const TimeRangeFilter = ({
  timeRangeOptions,
  selectedTimeRange,
  setSelectedTimeRange,
}) => (
  <div className="col-12 mb-4">
    <div className="card radius-8 border-0">
      <div className="card-body p-24">
        <div className="d-flex align-items-center justify-content-between flex-wrap gap-3">
          <div className="d-flex align-items-center gap-2">
            <Icon icon="solar:filter-outline" className="text-primary-600" />
            <span className="fw-medium text-secondary-light">Filters:</span>
            <span className="text-primary-600 fw-semibold">
              Time range: Last 30 days
            </span>
          </div>
          <Icon
            icon="solar:refresh-outline"
            className="text-secondary-light cursor-pointer"
          />
        </div>
        <div className="row mt-20">
          <div className="col-md-6">
            <div className="row g-3">
              {timeRangeOptions.map((option) => (
                <div key={option.id} className="col-6 col-sm-4">
                  <div className="form-check">
                    <input
                      className="form-check-input"
                      type="radio"
                      name="timeRange"
                      id={option.id}
                      checked={selectedTimeRange === option.id}
                      onChange={() => setSelectedTimeRange(option.id)}
                    />
                    <label
                      className="form-check-label text-sm"
                      htmlFor={option.id}
                    >
                      {option.label}
                    </label>
                  </div>
                </div>
              ))}
            </div>
          </div>
          <div className="col-md-6 d-flex justify-content-end align-items-end">
            <div className="d-flex gap-2">
              <button className="btn btn-outline-success px-20 py-8">
                Clear
              </button>
              <button className="btn btn-primary px-20 py-8">Apply</button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
);

export default TimeRangeFilter;
