import React, { useState, useEffect, useCallback } from "react";
import { Icon } from "@iconify/react/dist/iconify.js";
import MasterLayout from "../masterLayout/MasterLayout";
import Breadcrumb from "../components/Breadcrumb";
import ProfileForm from "../components/Profile/ProfileForm";
import PasswordForm from "../components/Profile/PasswordForm";
import ActivitiesTable from "../components/Profile/ActivitiesTable";
import ApiCredentials from "../components/Profile/ApiCredentials";
import { useProfile } from "../hooks/useProfile";
import { useAuth } from "../hooks/useAuth";
import { usePlans } from "../hooks/usePlans";

const Profile = () => {
  const [activeTab, setActiveTab] = useState("profile");
  const [profileData, setProfileData] = useState({
    firstName: "",
    lastName: "",
    email: "",
    phone: "",
    address: "",
    bio: "",
    username: "",
    taxOrId: "",
    type: "",
    status: "",
    apiKey: "",
    secretKey: "",
    createdAt: "",
  });
  const [activities, setActivities] = useState([]);
  const [errorMessage, setErrorMessage] = useState("");
  const [successMessage, setSuccessMessage] = useState("");
  const [isInitialLoading, setIsInitialLoading] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [planData, setPlanData] = useState(null);
  const [accountsData, setAccountsData] = useState(null);

  // API hooks
  const { user, logout } = useAuth();
  const {
    profile,
    getProfile,
    updateProfile,
    changePassword,
    getActivities,
    loading,
  } = useProfile();
  const { getAvailablePlans } = usePlans();

  const [passwordData, setPasswordData] = useState({
    currentPassword: "",
    newPassword: "",
    confirmPassword: "",
  });

  const [showPasswords, setShowPasswords] = useState({
    current: false,
    new: false,
    confirm: false,
  });

  // Load profile data on component mount
  useEffect(() => {
    const loadProfileData = async () => {
      setIsInitialLoading(true);
      try {
        const result = await getProfile();
        if (result.success && result.profile) {
          // Map API response fields to form fields based on actual API structure
          const profile = result.profile;
          setProfileData({
            firstName: profile.first_name || "",
            lastName: profile.last_name || "",
            email: profile.email || "",
            phone: profile.phone || "",
            address: profile.address || "",
            bio: profile.organization_name || "", // Using organization_name as bio
            username: profile.username || "",
            taxOrId: profile.tax_or_id || "",
            type: profile.type || "",
            status: profile.status || "",
            apiKey: profile.api_key || "",
            secretKey: profile.secret_key || "",
            createdAt: profile.created_at || "",
          });

          // Set plan data from profile
          if (profile.plan) {
            setPlanData(profile.plan);
          }

          // Set accounts data from profile
          if (profile.accounts) {
            setAccountsData(profile.accounts);
          }
        }

        // Load available plans to get more details
        try {
          const plansResult = await getAvailablePlans();
          if (plansResult.success && plansResult.data && planData) {
            // Find current plan details from available plans
            const currentPlanDetails = plansResult.data.find(
              (p) => p.id === planData.id
            );
            if (currentPlanDetails) {
              setPlanData(currentPlanDetails);
            }
          }
        } catch (planError) {
          console.warn("Could not load available plans:", planError);
        }
      } catch (error) {
        console.error("Error loading profile:", error);
        setErrorMessage("Không thể tải thông tin hồ sơ");
      } finally {
        setIsInitialLoading(false);
      }
    };

    if (user) {
      loadProfileData();
    }
  }, [user]); // Chỉ phụ thuộc vào user

  // Load activities when activities tab is active
  useEffect(() => {
    if (activeTab === "activities") {
      const loadActivities = async () => {
        try {
          const result = await getActivities({ limit: 20 });
          if (result.success && result.data) {
            setActivities(result.data);
          }
        } catch (error) {
          console.error("Error loading activities:", error);
        }
      };
      loadActivities();
    }
  }, [activeTab, getActivities]); // Phụ thuộc vào activeTab và getActivities

  // Auto hide success message after 2 seconds
  useEffect(() => {
    if (successMessage) {
      const timer = setTimeout(() => {
        // Add fade-out class before hiding
        const toastElement = document.querySelector(".success-toast");
        if (toastElement) {
          toastElement.classList.add("fade-out");
          setTimeout(() => {
            setSuccessMessage("");
          }, 300); // Wait for fade-out animation
        } else {
          setSuccessMessage("");
        }
      }, 2000);
      return () => clearTimeout(timer);
    }
  }, [successMessage]);

  const handleProfileChange = useCallback((e) => {
    const { name, value } = e.target;
    setProfileData((prev) => ({
      ...prev,
      [name]: value,
    }));
  }, []);

  const handlePasswordChange = useCallback((e) => {
    const { name, value } = e.target;
    setPasswordData((prev) => ({
      ...prev,
      [name]: value,
    }));
  }, []);

  const handleProfileSubmit = useCallback(
    async (e) => {
      e.preventDefault();
      setIsSubmitting(true);
      setErrorMessage("");
      setSuccessMessage("");

      try {
        // Chỉ gửi 3 trường có thể chỉnh sửa theo API Postman
        const updateData = {
          first_name: profileData.firstName,
          last_name: profileData.lastName,
          address: profileData.address,
        };

        const result = await updateProfile(updateData);
        if (result.success) {
          setSuccessMessage("Cập nhật thông tin thành công!");
        } else {
          setErrorMessage(result.message || "Cập nhật thông tin thất bại!");
        }
      } catch (error) {
        setErrorMessage("Có lỗi xảy ra khi cập nhật thông tin!");
      } finally {
        setIsSubmitting(false);
      }
    },
    [
      profileData.firstName,
      profileData.lastName,
      profileData.address,
      updateProfile,
    ]
  );

  const handlePasswordSubmit = useCallback(
    async (e) => {
      e.preventDefault();
      if (passwordData.newPassword !== passwordData.confirmPassword) {
        setErrorMessage("Mật khẩu xác nhận không khớp!");
        return;
      }

      setIsSubmitting(true);
      setErrorMessage("");
      setSuccessMessage("");

      try {
        const result = await changePassword({
          currentPassword: passwordData.currentPassword,
          newPassword: passwordData.newPassword,
        });

        if (result.success) {
          setSuccessMessage("Đổi mật khẩu thành công! Đang chuyển hướng...");
          setPasswordData({
            currentPassword: "",
            newPassword: "",
            confirmPassword: "",
          });

          // Sau 2 giây thì logout và chuyển về trang đăng nhập
          setTimeout(() => {
            logout();
          }, 2000);
        } else {
          setErrorMessage(result.message || "Đổi mật khẩu thất bại!");
        }
      } catch (error) {
        setErrorMessage("Có lỗi xảy ra khi đổi mật khẩu!");
      } finally {
        setIsSubmitting(false);
      }
    },
    [
      passwordData.currentPassword,
      passwordData.newPassword,
      passwordData.confirmPassword,
      changePassword,
      logout,
    ]
  );

  const togglePasswordVisibility = useCallback((field) => {
    setShowPasswords((prev) => ({
      ...prev,
      [field]: !prev[field],
    }));
  }, []);

  const handleTabChange = useCallback((tab) => {
    setActiveTab(tab);
    setErrorMessage("");
    setSuccessMessage("");
  }, []);

  return (
    <MasterLayout>
      <section className="row">
        <Breadcrumb title="Hồ sơ cá nhân" />

        <div className="col-12">
          <div className="card h-100 p-0 radius-12">
            <div className="card-header border-bottom bg-base py-16 px-24">
              <h6 className="text-lg fw-semibold mb-0">Thông tin tài khoản</h6>
            </div>
            <div className="card-body p-24">
              {/* Loading State */}
              {(isInitialLoading || loading) && (
                <div className="d-flex justify-content-center align-items-center py-32">
                  <div className="spinner-border text-primary" role="status">
                    <span className="visually-hidden">Loading...</span>
                  </div>
                </div>
              )}

              {/* Error Message */}
              {errorMessage && (
                <div className="alert alert-danger mb-24" role="alert">
                  <Icon icon="solar:danger-circle-outline" className="me-2" />
                  {errorMessage}
                </div>
              )}

              {/* Success Message */}
              {successMessage && (
                <div
                  className="position-fixed top-2 end-0 mb-5 me-4"
                  style={{ zIndex: 1050 }}
                >
                  <div className="success-toast text-white">
                    <div className="toast-content">
                      <div className="toast-icon">
                        <Icon
                          icon="solar:check-circle-bold"
                          width="16"
                          color="white"
                        />
                      </div>
                      <div className="toast-text">{successMessage}</div>
                    </div>
                  </div>
                </div>
              )}

              {/* Profile Header */}
              <div className="card border border-neutral-200 mb-24">
                <div className="profile-header">
                  <div className="profile-header-content">
                    <div className="profile-header-info">
                      <h5>
                        {profileData.firstName || profileData.lastName
                          ? `${profileData.firstName} ${profileData.lastName}`.trim()
                          : "Người dùng"}
                      </h5>
                      <p>
                        {profileData.type === "individual"
                          ? "Cá nhân"
                          : profileData.type === "business"
                          ? "Doanh nghiệp"
                          : "Không xác định"}
                      </p>
                    </div>
                    <div className="profile-header-actions">
                      <button
                        className={`btn ${
                          activeTab === "profile"
                            ? "btn-success"
                            : "btn-outline-secondary"
                        }`}
                        onClick={() => handleTabChange("profile")}
                        type="button"
                      >
                        <Icon icon="solar:user-outline" />
                        Thông tin cá nhân
                      </button>
                      <button
                        className={`btn ${
                          activeTab === "password"
                            ? "btn-success"
                            : "btn-outline-secondary"
                        }`}
                        onClick={() => handleTabChange("password")}
                        type="button"
                      >
                        <Icon icon="solar:lock-password-outline" />
                        Đổi mật khẩu
                      </button>
                      <button
                        className={`btn ${
                          activeTab === "activities"
                            ? "btn-success"
                            : "btn-outline-secondary"
                        }`}
                        onClick={() => handleTabChange("activities")}
                        type="button"
                      >
                        <Icon icon="solar:history-outline" />
                        Hoạt động
                      </button>
                      <button
                        className={`btn ${
                          activeTab === "credentials"
                            ? "btn-success"
                            : "btn-outline-secondary"
                        }`}
                        onClick={() => handleTabChange("credentials")}
                        type="button"
                      >
                        <Icon icon="solar:key-outline" />
                        API Credentials
                      </button>
                    </div>
                  </div>
                </div>
              </div>

              {/* Tab Content */}
              <div className="row">
                <div className="col-12">
                  {/* Tab Content */}
                  <div className="tab-content">
                    {/* Profile Tab */}
                    {activeTab === "profile" && (
                      <div className="tab-pane fade show active">
                        <ProfileForm
                          profileData={profileData}
                          plan={
                            planData || {
                              name: "Chưa có dữ liệu",
                              base_price: 0,
                              account_limit: 0,
                            }
                          }
                          accounts={
                            accountsData || {
                              total: 0,
                              active: 0,
                            }
                          }
                          statusInfo={{
                            icon:
                              profileData.status === "active" ||
                              profileData.status === 1 ||
                              profileData.status === "1"
                                ? "solar:check-circle-bold"
                                : "solar:close-circle-bold",
                            class:
                              profileData.status === "active" ||
                              profileData.status === 1 ||
                              profileData.status === "1"
                                ? "text-success-600"
                                : "text-danger-600",
                            text:
                              profileData.status === "active" ||
                              profileData.status === 1 ||
                              profileData.status === "1"
                                ? "Đã kích hoạt"
                                : "Chưa kích hoạt",
                          }}
                          handleProfileChange={handleProfileChange}
                          handleProfileSubmit={handleProfileSubmit}
                          isLoading={isSubmitting}
                        />
                      </div>
                    )}

                    {/* Password Tab */}
                    {activeTab === "password" && (
                      <div className="tab-pane fade show active">
                        <PasswordForm
                          passwordData={passwordData}
                          showPasswords={showPasswords}
                          handlePasswordChange={handlePasswordChange}
                          handlePasswordSubmit={handlePasswordSubmit}
                          togglePasswordVisibility={togglePasswordVisibility}
                          isLoading={isSubmitting}
                        />
                      </div>
                    )}

                    {/* Activities Tab */}
                    {activeTab === "activities" && (
                      <div className="tab-pane fade show active">
                        <ActivitiesTable activities={activities} />
                      </div>
                    )}

                    {/* API Credentials Tab */}
                    {activeTab === "credentials" && (
                      <div className="tab-pane fade show active">
                        <ApiCredentials
                          profileData={profileData}
                          setSuccessMessage={setSuccessMessage}
                        />
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </MasterLayout>
  );
};

export default Profile;
