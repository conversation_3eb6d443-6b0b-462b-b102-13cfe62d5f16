import { useState, useCallback, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useApi } from './useApi';

export const useAuth = () => {
  const navigate = useNavigate();
  const { apiCall, loading: apiLoading, error } = useApi();
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);

  // Check authentication on mount
  useEffect(() => {
    const checkAuth = async () => {
      const token = localStorage.getItem('authToken');
      const userData = localStorage.getItem('userData');

      if (token && userData) {
        try {
          // Use stored data directly to avoid unnecessary API calls
          // Profile data will be refreshed by Profile component when needed
          setUser(JSON.parse(userData));
        } catch (err) {
          console.error('Error parsing user data:', err);
          localStorage.removeItem('authToken');
          localStorage.removeItem('userData');
        }
      }
      setLoading(false);
    };

    checkAuth();
  }, []);

  // Login function
  const login = useCallback(async (credentials) => {
    try {
      const response = await apiCall('/partner/login', {
        method: 'POST',
        data: credentials,
      });

      if (response && (response.access_token || response.token)) {
        // Store token and user data
        const token = response.access_token || response.token;
        localStorage.setItem('authToken', token);

        // Try to get detailed profile data
        try {
          const profileResponse = await apiCall('/partner/profile', {
            method: 'GET',
            headers: {
              'Authorization': `Bearer ${token}`
            }
          });

          if (profileResponse && profileResponse.success && profileResponse.profile) {
            // Store detailed profile data
            localStorage.setItem('userData', JSON.stringify(profileResponse.profile));
            setUser(profileResponse.profile);
          } else {
            // Fallback to basic user data
            localStorage.setItem('userData', JSON.stringify(response.user || credentials));
            setUser(response.user || credentials);
          }
        } catch (profileError) {
          console.warn('Could not load profile data:', profileError);
          // Fallback to basic user data
          localStorage.setItem('userData', JSON.stringify(response.user || credentials));
          setUser(response.user || credentials);
        }

        return {
          success: true,
          data: response,
          message: 'Đăng nhập thành công'
        };
      } else {
        throw new Error('Phản hồi không hợp lệ từ máy chủ');
      }
    } catch (err) {
      // Extract error message from API response
      let errorMessage = 'Đăng nhập thất bại';

      if (err.message.includes('Mật khẩu không chính xác')) {
        errorMessage = 'Mật khẩu không chính xác';
      } else if (err.message.includes('Tài khoản không tồn tại')) {
        errorMessage = 'Tài khoản không tồn tại';
      } else if (err.message.includes('Network Error')) {
        errorMessage = 'Lỗi kết nối mạng. Vui lòng thử lại';
      } else if (err.message) {
        errorMessage = err.message;
      }

      return {
        success: false,
        message: errorMessage
      };
    }
  }, [apiCall]);

  // Logout function
  const logout = useCallback(() => {
    // Clear local storage and state
    localStorage.removeItem('authToken');
    localStorage.removeItem('userData');
    localStorage.removeItem('rememberMe');
    setUser(null);

    // Navigate to login page
    navigate('/login');
  }, [navigate]);

  // Check if user is authenticated
  const isAuthenticated = useCallback(() => {
    const token = localStorage.getItem('authToken');
    const userData = localStorage.getItem('userData');

    if (token && userData) {
      try {
        const parsedUser = JSON.parse(userData);
        setUser(parsedUser);
        return true;
      } catch (err) {
        console.error('Error parsing user data:', err);
        logout();
        return false;
      }
    }
    return false;
  }, [logout]);

  // Get current user
  const getCurrentUser = useCallback(() => {
    const userData = localStorage.getItem('userData');
    if (userData) {
      try {
        return JSON.parse(userData);
      } catch (err) {
        console.error('Error parsing user data:', err);
        return null;
      }
    }
    return null;
  }, []);

  // Refresh token
  const refreshToken = useCallback(async () => {
    try {
      const response = await apiCall('/auth/refresh', {
        method: 'POST',
      });

      if (response.success && response.data.token) {
        localStorage.setItem('authToken', response.data.token);
        return true;
      }
      return false;
    } catch (err) {
      console.error('Token refresh failed:', err);
      logout();
      return false;
    }
  }, [apiCall, logout]);

  return {
    user,
    login,
    logout,
    isAuthenticated,
    getCurrentUser,
    refreshToken,
    loading,
    error
  };
};

export default useAuth;
