{"name": "wowdash-vite-react", "homepage": "https://paybuild.test", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview", "serve": "serve -s dist"}, "dependencies": {"@dnd-kit/core": "^6.1.0", "@dnd-kit/modifiers": "^7.0.0", "@dnd-kit/sortable": "^8.0.0", "@fullcalendar/core": "^6.1.10", "@fullcalendar/daygrid": "^6.1.10", "@fullcalendar/interaction": "^6.1.10", "@fullcalendar/react": "^6.1.10", "@fullcalendar/timegrid": "^6.1.10", "@hello-pangea/dnd": "^17.0.0", "@hugeicons/react": "^1.0.5", "@iconify-json/solar": "^1.2.0", "@phosphor-icons/react": "^2.1.7", "@popperjs/core": "^2.11.8", "@ramonak/react-progress-bar": "^5.2.0", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "animate.css": "^4.1.1", "apexcharts": "^4.4.0", "axios": "^1.10.0", "bootstrap": "^5.3.7", "clsx": "^2.1.1", "datatables.net": "^2.3.2", "datatables.net-bs5": "^2.3.2", "datatables.net-dt": "^2.1.8", "flatpickr": "^4.6.13", "framer-motion": "^12.22.0", "gsap": "^3.13.0", "highlight.js": "^11.11.0", "isotope-layout": "^3.0.6", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "jsvectormap": "1.3.1", "lightgallery": "^2.8.2", "lightgallery.js": "^1.4.0", "masonry-layout": "^4.2.2", "react": "^18.2.0", "react-apexcharts": "^1.7.0", "react-beautiful-dnd": "^13.1.1", "react-datepicker": "^7.4.0", "react-dom": "^18.2.0", "react-fast-marquee": "^1.6.5", "react-helmet-async": "^2.0.5", "react-modal-video": "^2.0.2", "react-quill": "^2.0.0", "react-quill-new": "^3.3.3", "react-router-dom": "^6.22.1", "react-scripts": "5.0.1", "react-scroll-to-top": "^3.0.0", "react-slick": "^0.30.2", "react-slider": "^2.0.6", "react-toastify": "^10.0.5", "react-tsparticles": "^2.12.2", "recharts": "^3.0.2", "slick-carousel": "^1.8.1", "socket.io-client": "^4.8.1", "swiper": "^11.2.10", "tsparticles": "^3.8.1", "uuid": "^10.0.0", "web-vitals": "^2.1.4", "wowjs": "^1.1.3", "xlsx": "^0.18.5"}, "devDependencies": {"@eslint/js": "^9.19.0", "@iconify/react": "^5.0.2", "@types/react": "^19.0.8", "@types/react-dom": "^19.0.3", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.19.0", "eslint-plugin-react": "^7.37.4", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.18", "globals": "^15.14.0", "vite": "^6.1.0", "vite-plugin-svgr": "^4.3.0"}}