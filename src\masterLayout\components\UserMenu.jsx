import React from "react";
import { Link, useNavigate } from "react-router-dom";
import { Icon } from "@iconify/react/dist/iconify.js";
import { useAuth } from "../../hooks/useAuth";
import ProfileIcon from "../../assets/images/profile.png";

const UserMenu = () => {
  const navigate = useNavigate();
  const { user, logout } = useAuth();

  const handleLogout = () => {
    logout();
    navigate("/login");
  };

  // Get user display data
  const getDisplayName = () => {
    if (!user) return "Xin chào User";

    // Hiển thị "Xin chào [first_name]"
    if (user.last_name) {
      return `Xin chào ${user.last_name}!`;
    }
    if (user.username) {
      return `Xin chào ${user.username}!`;
    }
    return "Xin chào User";
  };

  const getAccountType = () => {
    if (!user) return "Đang tải...";

    // Map user type to display text
    if (user.type === "individual") return "Cá nhân";
    if (user.type === "business") return "Doanh nghiệp";
    if (user.type === "organization") return "Tổ chức";

    // Hiển thị type thực tế từ API
    return user.type || "Chưa xác định";
  };

  return (
    <div className="dropdown user-button">
      <button
        className="d-flex justify-content-center align-items-center rounded-circle border-0 p-0"
        type="button"
        data-bs-toggle="dropdown"
        aria-expanded="false"
      >
        <img
          src={ProfileIcon}
          alt="User Avatar"
          className="w-40-px h-40-px object-fit-cover rounded-circle"
        />
      </button>
      <div className="dropdown-menu to-top dropdown-menu-sm">
        <div className="py-12 px-16 radius-8 bg-primary-50 mb-16 d-flex align-items-center justify-content-between gap-2">
          <div>
            <h6 className="text-lg text-primary-light fw-semibold mb-2">
              {getDisplayName()}
            </h6>
            <span className="text-secondary-light fw-medium text-sm">
              {getAccountType()}
            </span>
          </div>
        </div>
        <ul className="list-unstyled px-16">
          <li>
            <Link
              to="/profile"
              className="dropdown-item text-black px-0 py-8 hover-bg-transparent hover-text-primary d-flex align-items-center gap-3"
            >
              <Icon icon="solar:user-linear" className="icon text-xl" /> Hồ sơ
              cá nhân
            </Link>
          </li>
          <li>
            <button
              className="dropdown-item text-black px-0 py-8 hover-bg-transparent hover-text-danger d-flex align-items-center gap-3 bg-transparent border-0 w-100 text-start"
              onClick={handleLogout}
            >
              <Icon icon="lucide:power" className="icon text-xl" /> Đăng xuất
            </button>
          </li>
        </ul>
      </div>
    </div>
  );
};

export default UserMenu;
