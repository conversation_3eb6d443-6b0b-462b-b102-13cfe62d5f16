import React, { useState, useEffect, useCallback } from "react";
import MasterLayout from "../masterLayout/MasterLayout";
import Breadcrumb from "../components/Breadcrumb";
import TimeRangeFilter from "../components/TimeRangeFilter";
import StatisticsCards from "../components/StatisticsCards";
import Bar<PERSON>hart from "../components/BarChart";
import StatusPieChart from "../components/StatusPieChart";
import { Icon } from "@iconify/react/dist/iconify.js";
import { useAnalytics } from "../hooks/useAnalytics";
import ApiTester from "../components/ApiTester";

const DashBoard = () => {
  const [selectedTimeRange, setSelectedTimeRange] = useState("last30days");
  const [isLoading, setIsLoading] = useState(true);
  const [analyticsOverview, setAnalyticsOverview] = useState(null);
  const [callsOverTimeData, setCallsOverTimeData] = useState([]);
  const [topEndpointsData, setTopEndpointsData] = useState([]);
  const [statusCodeData, setStatusCodeData] = useState([]);
  const [callHistoryData, setCallHistoryData] = useState([]);
  const [showApiTester, setShowApiTester] = useState(false);

  // API hooks - chỉ sử dụng Analytics APIs
  const {
    getAnalyticsOverview,
    getCallsOverTime,
    getTopEndpoints,
    getStatusCodes,
    getCallHistory,
    loading: analyticsLoading,
  } = useAnalytics();

  // Data arrays
  const timeRangeOptions = [
    { id: "last15min", label: "Last 15 minutes" },
    { id: "last12hours", label: "Last 12 hours" },
    { id: "custom", label: "Custom" },
    { id: "last30min", label: "Last 30 minutes" },
    { id: "last24hours", label: "Last 24 hours" },
    { id: "last1hour", label: "Last 1 hour" },
    { id: "last7days", label: "Last 7 days" },
    { id: "last4hours", label: "Last 4 hours" },
    { id: "last30days", label: "Last 30 days" },
  ];

  // Load analytics data cho dashboard
  const loadAnalyticsData = useCallback(async () => {
    setIsLoading(true);
    try {
      // Xác định time range
      const timeRange =
        selectedTimeRange === "last7days"
          ? 7
          : selectedTimeRange === "last30days"
          ? 30
          : 7;

      // Load analytics overview - chứa tổng quan về API usage
      const overviewResult = await getAnalyticsOverview({ timeRange });
      if (overviewResult.success) {
        setAnalyticsOverview(overviewResult.data);
      }

      // Load calls over time data cho biểu đồ
      const callsResult = await getCallsOverTime({
        timeRange,
        groupBy: "hour",
      });
      if (callsResult.success) {
        setCallsOverTimeData(callsResult.data);
      }

      // Load top endpoints data
      const endpointsResult = await getTopEndpoints({ timeRange, limit: 10 });
      if (endpointsResult.success) {
        setTopEndpointsData(endpointsResult.data);
      }

      // Load status code distribution cho pie chart
      const statusResult = await getStatusCodes({ timeRange });
      if (statusResult.success) {
        setStatusCodeData(statusResult.data);
      }

      // Load call history
      const historyResult = await getCallHistory({
        page: 1,
        limit: 25,
        timeRange,
      });
      if (historyResult.success) {
        setCallHistoryData(historyResult.data);
      }
    } catch (error) {
      console.error("Error loading analytics data:", error);
    } finally {
      setIsLoading(false);
    }
  }, [
    selectedTimeRange,
    getAnalyticsOverview,
    getCallsOverTime,
    getTopEndpoints,
    getStatusCodes,
    getCallHistory,
  ]);

  // Load data on component mount and when time range changes
  useEffect(() => {
    loadAnalyticsData();
  }, [loadAnalyticsData]);

  // Generate statistics cards từ analytics overview data
  const statisticsCards = analyticsOverview
    ? [
        {
          title: "Total calls",
          value: analyticsOverview.total_calls?.toString() || "0",
          subtitle: "API calls",
        },
        {
          title: "Total errors",
          value: analyticsOverview.total_errors?.toString() || "0",
          subtitle: "errors",
        },
        {
          title: "Average response time",
          value: analyticsOverview.avg_response_time?.toString() || "0",
          subtitle: "milliseconds",
        },
      ]
    : [
        // Dummy data khi API không có dữ liệu
        { title: "Total calls", value: "12,847", subtitle: "API calls" },
        { title: "Total errors", value: "234", subtitle: "errors" },
        {
          title: "Average response time",
          value: "285",
          subtitle: "milliseconds",
        },
      ];

  // Transform calls over time data cho BarChart component
  const chartData =
    callsOverTimeData.length > 0
      ? callsOverTimeData.map((item) => ({
          // Transform API data format to match BarChart component expectations
          stream0: item.successful_calls || 0,
          stream1: item.error_calls || 0,
          stream2: item.total_calls || 0,
          timestamp: item.timestamp,
          hour: item.hour,
        }))
      : [
          // Dummy data khi API không có dữ liệu
          { stream0: 120, stream1: 15, stream2: 135 },
          { stream0: 98, stream1: 22, stream2: 120 },
          { stream0: 156, stream1: 8, stream2: 164 },
          { stream0: 203, stream1: 31, stream2: 234 },
          { stream0: 178, stream1: 19, stream2: 197 },
          { stream0: 245, stream1: 12, stream2: 257 },
          { stream0: 189, stream1: 28, stream2: 217 },
          { stream0: 267, stream1: 35, stream2: 302 },
          { stream0: 234, stream1: 17, stream2: 251 },
          { stream0: 298, stream1: 42, stream2: 340 },
          { stream0: 276, stream1: 23, stream2: 299 },
          { stream0: 321, stream1: 38, stream2: 359 },
          { stream0: 345, stream1: 29, stream2: 374 },
          { stream0: 389, stream1: 45, stream2: 434 },
          { stream0: 412, stream1: 33, stream2: 445 },
          { stream0: 456, stream1: 52, stream2: 508 },
          { stream0: 434, stream1: 41, stream2: 475 },
          { stream0: 478, stream1: 36, stream2: 514 },
          { stream0: 502, stream1: 48, stream2: 550 },
          { stream0: 467, stream1: 39, stream2: 506 },
          { stream0: 523, stream1: 44, stream2: 567 },
          { stream0: 498, stream1: 37, stream2: 535 },
          { stream0: 445, stream1: 32, stream2: 477 },
          { stream0: 389, stream1: 28, stream2: 417 },
        ];

  // Transform status code data cho StatusPieChart component
  const statusCodeChartData =
    statusCodeData.length > 0
      ? statusCodeData.map((item) => ({
          color:
            item.status_code === 200
              ? "success-500"
              : item.status_code >= 400 && item.status_code < 500
              ? "danger-500"
              : item.status_code >= 500
              ? "warning-500"
              : "info-500",
          label: `${item.status_code} ${item.status_text || ""}`,
          percentage: `${item.percentage || 0}%`,
          count: item.count || 0,
        }))
      : [
          // Dummy data khi API không có dữ liệu
          {
            color: "success-500",
            label: "200 OK",
            percentage: "78.5%",
            count: 10089,
          },
          {
            color: "info-500",
            label: "201 Created",
            percentage: "12.3%",
            count: 1581,
          },
          {
            color: "danger-500",
            label: "400 Bad Request",
            percentage: "5.8%",
            count: 745,
          },
          {
            color: "warning-500",
            label: "500 Internal Error",
            percentage: "2.1%",
            count: 270,
          },
          {
            color: "secondary",
            label: "404 Not Found",
            percentage: "1.3%",
            count: 167,
          },
        ];

  return (
    <>
      <MasterLayout>
        <section className="row">
          <Breadcrumb title="Tổng quan" />
          <TimeRangeFilter
            timeRangeOptions={timeRangeOptions}
            selectedTimeRange={selectedTimeRange}
            setSelectedTimeRange={setSelectedTimeRange}
          />
          <StatisticsCards statisticsCards={statisticsCards} />
        </section>

        {/* API Tester Section */}
        <section className="row mb-4">
          <div className="col-12">
            <div className="d-flex justify-content-between align-items-center mb-3">
              <h6 className="mb-0">API Integration Status</h6>
              <button
                className="btn btn-outline-primary btn-sm"
                onClick={() => setShowApiTester(!showApiTester)}
              >
                {showApiTester ? "Ẩn API Tester" : "Hiện API Tester"}
              </button>
            </div>
            {showApiTester && <ApiTester />}
          </div>
        </section>

        {isLoading ? (
          <section className="row">
            <div className="col-12 text-center py-5">
              <div className="spinner-border text-primary" role="status">
                <span className="visually-hidden">Loading...</span>
              </div>
              <p className="mt-3">Đang tải dữ liệu dashboard...</p>
            </div>
          </section>
        ) : (
          <section>
            <div className="row">
              <BarChart chartData={chartData} />
              <StatusPieChart statusCodeData={statusCodeChartData} />
            </div>
          </section>
        )}
      </MasterLayout>
    </>
  );
};

export default DashBoard;
