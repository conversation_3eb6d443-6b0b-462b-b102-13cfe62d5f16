import React, { useState, useEffect, useCallback } from "react";
import MasterLayout from "../masterLayout/MasterLayout";
import Breadcrumb from "../components/Breadcrumb";
import TimeRangeFilter from "../components/TimeRangeFilter";
import StatisticsCards from "../components/StatisticsCards";
import Bar<PERSON>hart from "../components/BarChart";
import StatusPieChart from "../components/StatusPieChart";
import { Icon } from "@iconify/react/dist/iconify.js";
import { useAnalytics } from "../hooks/useAnalytics";
import ApiTester from "../components/ApiTester";

const DashBoard = () => {
  const [selectedTimeRange, setSelectedTimeRange] = useState("last30days");
  const [isLoading, setIsLoading] = useState(true);
  const [analyticsOverview, setAnalyticsOverview] = useState(null);
  const [callsOverTimeData, setCallsOverTimeData] = useState([]);
  const [topEndpointsData, setTopEndpointsData] = useState([]);
  const [statusCodeData, setStatusCodeData] = useState([]);
  const [callHistoryData, setCallHistoryData] = useState([]);
  const [showApiTester, setShowApiTester] = useState(false);

  // API hooks - chỉ sử dụng Analytics APIs
  const {
    getAnalyticsOverview,
    getCallsOverTime,
    getTopEndpoints,
    getStatusCodes,
    getCallHistory,
    loading: analyticsLoading,
  } = useAnalytics();

  // Data arrays
  const timeRangeOptions = [
    { id: "last15min", label: "Last 15 minutes" },
    { id: "last12hours", label: "Last 12 hours" },
    { id: "custom", label: "Custom" },
    { id: "last30min", label: "Last 30 minutes" },
    { id: "last24hours", label: "Last 24 hours" },
    { id: "last1hour", label: "Last 1 hour" },
    { id: "last7days", label: "Last 7 days" },
    { id: "last4hours", label: "Last 4 hours" },
    { id: "last30days", label: "Last 30 days" },
  ];

  // Load analytics data cho dashboard
  const loadAnalyticsData = useCallback(async () => {
    setIsLoading(true);
    try {
      // Xác định time range
      const timeRange =
        selectedTimeRange === "last7days"
          ? 7
          : selectedTimeRange === "last30days"
          ? 30
          : 7;

      // Load analytics overview - chứa tổng quan về API usage
      const overviewResult = await getAnalyticsOverview({ timeRange });
      if (overviewResult.success) {
        setAnalyticsOverview(overviewResult.data);
      }

      // Load calls over time data cho biểu đồ
      const callsResult = await getCallsOverTime({
        timeRange,
        groupBy: "hour",
      });
      if (callsResult.success) {
        setCallsOverTimeData(callsResult.data);
      }

      // Load top endpoints data
      const endpointsResult = await getTopEndpoints({ timeRange, limit: 10 });
      if (endpointsResult.success) {
        setTopEndpointsData(endpointsResult.data);
      }

      // Load status code distribution cho pie chart
      const statusResult = await getStatusCodes({ timeRange });
      if (statusResult.success) {
        setStatusCodeData(statusResult.data);
      }

      // Load call history
      const historyResult = await getCallHistory({
        page: 1,
        limit: 25,
        timeRange,
      });
      if (historyResult.success) {
        setCallHistoryData(historyResult.data);
      }
    } catch (error) {
      console.error("Error loading analytics data:", error);
    } finally {
      setIsLoading(false);
    }
  }, [
    selectedTimeRange,
    getAnalyticsOverview,
    getCallsOverTime,
    getTopEndpoints,
    getStatusCodes,
    getCallHistory,
  ]);

  // Load data on component mount and when time range changes
  useEffect(() => {
    loadAnalyticsData();
  }, [loadAnalyticsData]);

  // Generate statistics cards từ analytics overview data hoặc dummy data
  const statisticsCards =
    analyticsOverview && analyticsOverview.total_calls
      ? [
          {
            title: "Total calls",
            value: analyticsOverview.total_calls?.toString() || "0",
            subtitle: "API calls",
          },
          {
            title: "Total errors",
            value: analyticsOverview.total_errors?.toString() || "0",
            subtitle: "errors",
          },
          {
            title: "Average response time",
            value: analyticsOverview.avg_response_time?.toString() || "0",
            subtitle: "milliseconds",
          },
        ]
      : [
          // Dummy data khi API không có dữ liệu
          { title: "Total calls", value: "12,847", subtitle: "API calls" },
          { title: "Total errors", value: "234", subtitle: "errors" },
          {
            title: "Average response time",
            value: "285",
            subtitle: "milliseconds",
          },
        ];

  // Transform calls over time data cho BarChart component
  const chartData =
    callsOverTimeData.length > 0
      ? callsOverTimeData.map((item) => ({
          // Transform API data format to match BarChart component expectations
          stream0: item.successful_calls || 0,
          stream1: item.error_calls || 0,
          stream2: item.total_calls || 0,
          timestamp: item.timestamp,
          hour: item.hour,
        }))
      : [
          // Dummy data khi API không có dữ liệu - scale phù hợp với BarChart (0-7.8)
          {
            stream0: 1.2,
            stream1: 0.3,
            stream2: 1.5,
            hour: "00:00",
            timestamp: "2024-01-01T00:00:00Z",
          },
          {
            stream0: 1.8,
            stream1: 0.4,
            stream2: 2.2,
            hour: "01:00",
            timestamp: "2024-01-01T01:00:00Z",
          },
          {
            stream0: 2.1,
            stream1: 0.2,
            stream2: 2.3,
            hour: "02:00",
            timestamp: "2024-01-01T02:00:00Z",
          },
          {
            stream0: 2.8,
            stream1: 0.5,
            stream2: 3.3,
            hour: "03:00",
            timestamp: "2024-01-01T03:00:00Z",
          },
          {
            stream0: 3.2,
            stream1: 0.3,
            stream2: 3.5,
            hour: "04:00",
            timestamp: "2024-01-01T04:00:00Z",
          },
          {
            stream0: 3.8,
            stream1: 0.4,
            stream2: 4.2,
            hour: "05:00",
            timestamp: "2024-01-01T05:00:00Z",
          },
          {
            stream0: 4.1,
            stream1: 0.6,
            stream2: 4.7,
            hour: "06:00",
            timestamp: "2024-01-01T06:00:00Z",
          },
          {
            stream0: 4.5,
            stream1: 0.5,
            stream2: 5.0,
            hour: "07:00",
            timestamp: "2024-01-01T07:00:00Z",
          },
          {
            stream0: 4.8,
            stream1: 0.4,
            stream2: 5.2,
            hour: "08:00",
            timestamp: "2024-01-01T08:00:00Z",
          },
          {
            stream0: 5.2,
            stream1: 0.7,
            stream2: 5.9,
            hour: "09:00",
            timestamp: "2024-01-01T09:00:00Z",
          },
          {
            stream0: 5.5,
            stream1: 0.5,
            stream2: 6.0,
            hour: "10:00",
            timestamp: "2024-01-01T10:00:00Z",
          },
          {
            stream0: 5.8,
            stream1: 0.6,
            stream2: 6.4,
            hour: "11:00",
            timestamp: "2024-01-01T11:00:00Z",
          },
          {
            stream0: 6.1,
            stream1: 0.4,
            stream2: 6.5,
            hour: "12:00",
            timestamp: "2024-01-01T12:00:00Z",
          },
          {
            stream0: 6.4,
            stream1: 0.7,
            stream2: 7.1,
            hour: "13:00",
            timestamp: "2024-01-01T13:00:00Z",
          },
          {
            stream0: 6.7,
            stream1: 0.5,
            stream2: 7.2,
            hour: "14:00",
            timestamp: "2024-01-01T14:00:00Z",
          },
          {
            stream0: 7.0,
            stream1: 0.8,
            stream2: 7.8,
            hour: "15:00",
            timestamp: "2024-01-01T15:00:00Z",
          },
          {
            stream0: 6.8,
            stream1: 0.6,
            stream2: 7.4,
            hour: "16:00",
            timestamp: "2024-01-01T16:00:00Z",
          },
          {
            stream0: 6.5,
            stream1: 0.5,
            stream2: 7.0,
            hour: "17:00",
            timestamp: "2024-01-01T17:00:00Z",
          },
          {
            stream0: 6.2,
            stream1: 0.7,
            stream2: 6.9,
            hour: "18:00",
            timestamp: "2024-01-01T18:00:00Z",
          },
          {
            stream0: 5.9,
            stream1: 0.4,
            stream2: 6.3,
            hour: "19:00",
            timestamp: "2024-01-01T19:00:00Z",
          },
          {
            stream0: 5.6,
            stream1: 0.6,
            stream2: 6.2,
            hour: "20:00",
            timestamp: "2024-01-01T20:00:00Z",
          },
          {
            stream0: 5.2,
            stream1: 0.5,
            stream2: 5.7,
            hour: "21:00",
            timestamp: "2024-01-01T21:00:00Z",
          },
          {
            stream0: 4.8,
            stream1: 0.4,
            stream2: 5.2,
            hour: "22:00",
            timestamp: "2024-01-01T22:00:00Z",
          },
          {
            stream0: 4.2,
            stream1: 0.3,
            stream2: 4.5,
            hour: "23:00",
            timestamp: "2024-01-01T23:00:00Z",
          },
        ];

  // Transform status code data cho StatusPieChart component
  const statusCodeChartData =
    statusCodeData.length > 0
      ? statusCodeData.map((item) => ({
          color:
            item.status_code === 200
              ? "success-500"
              : item.status_code >= 400 && item.status_code < 500
              ? "danger-500"
              : item.status_code >= 500
              ? "warning-500"
              : "info-500",
          label: `${item.status_code} ${item.status_text || ""}`,
          percentage: `${item.percentage || 0}%`,
          count: item.count || 0,
        }))
      : [
          // Dummy data khi API không có dữ liệu
          {
            color: "success-500",
            label: "200 OK",
            percentage: "78.5%",
            count: 10089,
          },
          {
            color: "info-500",
            label: "201 Created",
            percentage: "12.3%",
            count: 1581,
          },
          {
            color: "danger-500",
            label: "400 Bad Request",
            percentage: "5.8%",
            count: 745,
          },
          {
            color: "warning-500",
            label: "500 Internal Error",
            percentage: "2.1%",
            count: 270,
          },
          {
            color: "secondary",
            label: "404 Not Found",
            percentage: "1.3%",
            count: 167,
          },
        ];

  return (
    <>
      <MasterLayout>
        <section className="row">
          <Breadcrumb title="Tổng quan" />
          <TimeRangeFilter
            timeRangeOptions={timeRangeOptions}
            selectedTimeRange={selectedTimeRange}
            setSelectedTimeRange={setSelectedTimeRange}
          />
          <StatisticsCards statisticsCards={statisticsCards} />
        </section>

        {/* API Tester Section */}
        <section className="row mb-4">
          <div className="col-12">
            <div className="d-flex justify-content-between align-items-center mb-3">
              <h6 className="mb-0">API Integration Status</h6>
              <button
                className="btn btn-outline-primary btn-sm"
                onClick={() => setShowApiTester(!showApiTester)}
              >
                {showApiTester ? "Ẩn API Tester" : "Hiện API Tester"}
              </button>
            </div>
            {showApiTester && <ApiTester />}
          </div>
        </section>

        {isLoading ? (
          <section className="row">
            <div className="col-12 text-center py-5">
              <div className="spinner-border text-primary" role="status">
                <span className="visually-hidden">Loading...</span>
              </div>
              <p className="mt-3">Đang tải dữ liệu dashboard...</p>
            </div>
          </section>
        ) : (
          <section>
            <div className="row">
              <BarChart chartData={chartData} />
              <StatusPieChart statusCodeData={statusCodeChartData} />
            </div>
          </section>
        )}
      </MasterLayout>
    </>
  );
};

export default DashBoard;
