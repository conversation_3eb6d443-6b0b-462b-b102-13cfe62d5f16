import React, { useState, useEffect, useCallback } from "react";
import MasterLayout from "../masterLayout/MasterLayout";
import Breadcrumb from "../components/Breadcrumb";
import TimeRangeFilter from "../components/TimeRangeFilter";
import StatisticsCards from "../components/StatisticsCards";
import Bar<PERSON>hart from "../components/BarChart";
import StatusPieChart from "../components/StatusPieChart";
import { Icon } from "@iconify/react/dist/iconify.js";
import { useDashboard } from "../hooks/useDashboard";
import { useAnalytics } from "../hooks/useAnalytics";
import ApiTester from "../components/ApiTester";

const DashBoard = () => {
  const [selectedTimeRange, setSelectedTimeRange] = useState("last30days");
  const [isLoading, setIsLoading] = useState(true);
  const [dashboardData, setDashboardData] = useState(null);
  const [analyticsData, setAnalyticsData] = useState(null);
  const [usageChartData, setUsageChartData] = useState([]);
  const [statusCodeData, setStatusCodeData] = useState([]);
  const [showApiTester, setShowApiTester] = useState(false);

  // API hooks
  const {
    getDashboardOverview,
    getUsageChartData,
    getQuickStats,
    loading: dashboardLoading,
  } = useDashboard();
  const {
    getAnalyticsOverview,
    getStatusCodes,
    getCallsOverTime,
    loading: analyticsLoading,
  } = useAnalytics();

  // Data arrays
  const timeRangeOptions = [
    { id: "last15min", label: "Last 15 minutes" },
    { id: "last12hours", label: "Last 12 hours" },
    { id: "custom", label: "Custom" },
    { id: "last30min", label: "Last 30 minutes" },
    { id: "last24hours", label: "Last 24 hours" },
    { id: "last1hour", label: "Last 1 hour" },
    { id: "last7days", label: "Last 7 days" },
    { id: "last4hours", label: "Last 4 hours" },
    { id: "last30days", label: "Last 30 days" },
  ];

  // Load dashboard data
  const loadDashboardData = useCallback(async () => {
    setIsLoading(true);
    try {
      // Load dashboard overview
      const overviewResult = await getDashboardOverview();
      if (overviewResult.success) {
        setDashboardData(overviewResult.data);
      }

      // Load quick stats
      const statsResult = await getQuickStats();
      if (statsResult.success) {
        // Transform API data to match component format
        const stats = statsResult.data;
        // Assuming API returns: { total_calls, total_errors, avg_response_time }
      }

      // Load usage chart data based on selected time range
      const period =
        selectedTimeRange === "last7days"
          ? 7
          : selectedTimeRange === "last30days"
          ? 30
          : 7;
      const chartResult = await getUsageChartData(period);
      if (chartResult.success) {
        setUsageChartData(chartResult.data);
      }

      // Load analytics data for status codes
      const analyticsResult = await getAnalyticsOverview({ timeRange: period });
      if (analyticsResult.success) {
        setAnalyticsData(analyticsResult.data);
      }

      // Load status code distribution
      const statusResult = await getStatusCodes({ timeRange: period });
      if (statusResult.success) {
        setStatusCodeData(statusResult.data);
      }
    } catch (error) {
      console.error("Error loading dashboard data:", error);
    } finally {
      setIsLoading(false);
    }
  }, [
    selectedTimeRange,
    getDashboardOverview,
    getQuickStats,
    getUsageChartData,
    getAnalyticsOverview,
    getStatusCodes,
  ]);

  // Load data on component mount and when time range changes
  useEffect(() => {
    loadDashboardData();
  }, [loadDashboardData]);

  // Generate statistics cards from API data or fallback to default
  const statisticsCards = dashboardData
    ? [
        {
          title: "Total calls",
          value: dashboardData.total_calls?.toString() || "0",
          subtitle: "API calls",
        },
        {
          title: "Total errors",
          value: dashboardData.total_errors?.toString() || "0",
          subtitle: "errors",
        },
        {
          title: "Average response time",
          value: dashboardData.avg_response_time?.toString() || "0",
          subtitle: "milliseconds",
        },
      ]
    : [
        { title: "Total calls", value: "Loading...", subtitle: "API calls" },
        { title: "Total errors", value: "Loading...", subtitle: "errors" },
        {
          title: "Average response time",
          value: "Loading...",
          subtitle: "milliseconds",
        },
      ];

  // Use chart data from API or fallback to empty array
  const chartData = usageChartData.length > 0 ? usageChartData : [];

  // Use status code data from API or fallback to default
  const statusCodeChartData =
    statusCodeData.length > 0
      ? statusCodeData
      : [
          { color: "success-500", label: "200 OK", percentage: "Loading..." },
          {
            color: "info-500",
            label: "200 Processed",
            percentage: "Loading...",
          },
          {
            color: "danger-500",
            label: "400 Bad Request",
            percentage: "Loading...",
          },
          {
            color: "warning-500",
            label: "500 Internal S...",
            percentage: "Loading...",
          },
        ];

  return (
    <>
      <MasterLayout>
        <section className="row">
          <Breadcrumb title="Tổng quan" />
          <TimeRangeFilter
            timeRangeOptions={timeRangeOptions}
            selectedTimeRange={selectedTimeRange}
            setSelectedTimeRange={setSelectedTimeRange}
          />
          <StatisticsCards statisticsCards={statisticsCards} />
        </section>

        {/* API Tester Section */}
        <section className="row mb-4">
          <div className="col-12">
            <div className="d-flex justify-content-between align-items-center mb-3">
              <h6 className="mb-0">API Integration Status</h6>
              <button
                className="btn btn-outline-primary btn-sm"
                onClick={() => setShowApiTester(!showApiTester)}
              >
                {showApiTester ? "Ẩn API Tester" : "Hiện API Tester"}
              </button>
            </div>
            {showApiTester && <ApiTester />}
          </div>
        </section>

        {isLoading ? (
          <section className="row">
            <div className="col-12 text-center py-5">
              <div className="spinner-border text-primary" role="status">
                <span className="visually-hidden">Loading...</span>
              </div>
              <p className="mt-3">Đang tải dữ liệu dashboard...</p>
            </div>
          </section>
        ) : (
          <section>
            <div className="row">
              <BarChart chartData={chartData} />
              <StatusPieChart statusCodeData={statusCodeChartData} />
            </div>
          </section>
        )}
      </MasterLayout>
    </>
  );
};

export default DashBoard;
