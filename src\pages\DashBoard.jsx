import React, { useState } from "react";
import MasterLayout from "../masterLayout/MasterLayout";
import Breadcrumb from "../components/Breadcrumb";
import TimeRangeFilter from "../components/TimeRangeFilter";
import StatisticsCards from "../components/StatisticsCards";
import Bar<PERSON>hart from "../components/BarChart";
import Status<PERSON>ieChart from "../components/StatusPieChart";
import { Icon } from "@iconify/react/dist/iconify.js";

const DashBoard = () => {
  const [selectedTimeRange, setSelectedTimeRange] = useState("last30days");

  // Data arrays
  const timeRangeOptions = [
    { id: "last15min", label: "Last 15 minutes" },
    { id: "last12hours", label: "Last 12 hours" },
    { id: "custom", label: "Custom" },
    { id: "last30min", label: "Last 30 minutes" },
    { id: "last24hours", label: "Last 24 hours" },
    { id: "last1hour", label: "Last 1 hour" },
    { id: "last7days", label: "Last 7 days" },
    { id: "last4hours", label: "Last 4 hours" },
    { id: "last30days", label: "Last 30 days" },
  ];

  const statisticsCards = [
    { title: "Total calls", value: "513", subtitle: "API calls" },
    { title: "Total errors", value: "34", subtitle: "errors" },
    { title: "Average response time", value: "485", subtitle: "milliseconds" },
  ];

  const chartData = [
    // Stream0 (blue), Stream1 (light blue), Stream2 (orange)
    { stream0: 0.1, stream1: 0.05, stream2: 0.02 },
    { stream0: 0.2, stream1: 0.1, stream2: 0.05 },
    { stream0: 0.15, stream1: 0.08, stream2: 0.03 },
    { stream0: 0.3, stream1: 0.15, stream2: 0.08 },
    { stream0: 0.25, stream1: 0.12, stream2: 0.06 },
    { stream0: 0.4, stream1: 0.2, stream2: 0.1 },
    { stream0: 0.35, stream1: 0.18, stream2: 0.09 },
    { stream0: 0.5, stream1: 0.25, stream2: 0.12 },
    { stream0: 0.45, stream1: 0.22, stream2: 0.11 },
    { stream0: 0.6, stream1: 0.3, stream2: 0.15 },
    { stream0: 0.55, stream1: 0.28, stream2: 0.14 },
    { stream0: 0.7, stream1: 0.35, stream2: 0.18 },
    { stream0: 0.8, stream1: 0.4, stream2: 0.2 },
    { stream0: 1.2, stream1: 0.6, stream2: 0.3 },
    { stream0: 1.5, stream1: 0.75, stream2: 0.38 },
    { stream0: 2.0, stream1: 1.0, stream2: 0.5 },
    { stream0: 2.5, stream1: 1.25, stream2: 0.62 },
    { stream0: 3.0, stream1: 1.5, stream2: 0.75 },
    { stream0: 3.5, stream1: 1.75, stream2: 0.88 },
    { stream0: 4.0, stream1: 2.0, stream2: 1.0 },
    { stream0: 4.5, stream1: 2.25, stream2: 1.12 },
    { stream0: 5.0, stream1: 2.5, stream2: 1.25 },
    { stream0: 5.5, stream1: 2.75, stream2: 1.38 },
    { stream0: 6.0, stream1: 3.0, stream2: 1.5 },
    { stream0: 6.5, stream1: 3.25, stream2: 1.62 },
    { stream0: 7.0, stream1: 3.5, stream2: 1.75 },
    { stream0: 7.5, stream1: 3.75, stream2: 1.88 },
    { stream0: 7.8, stream1: 3.9, stream2: 1.95 },
    { stream0: 7.6, stream1: 3.8, stream2: 1.9 },
    { stream0: 7.2, stream1: 3.6, stream2: 1.8 },
    { stream0: 6.8, stream1: 3.4, stream2: 1.7 },
    { stream0: 6.4, stream1: 3.2, stream2: 1.6 },
    { stream0: 6.0, stream1: 3.0, stream2: 1.5 },
    { stream0: 5.5, stream1: 2.75, stream2: 1.38 },
    { stream0: 5.0, stream1: 2.5, stream2: 1.25 },
    { stream0: 4.5, stream1: 2.25, stream2: 1.12 },
    { stream0: 4.0, stream1: 2.0, stream2: 1.0 },
    { stream0: 3.5, stream1: 1.75, stream2: 0.88 },
    { stream0: 3.0, stream1: 1.5, stream2: 0.75 },
    { stream0: 2.5, stream1: 1.25, stream2: 0.62 },
    { stream0: 2.0, stream1: 1.0, stream2: 0.5 },
    { stream0: 1.5, stream1: 0.75, stream2: 0.38 },
    { stream0: 1.2, stream1: 0.6, stream2: 0.3 },
    { stream0: 1.0, stream1: 0.5, stream2: 0.25 },
    { stream0: 0.8, stream1: 0.4, stream2: 0.2 },
    { stream0: 0.6, stream1: 0.3, stream2: 0.15 },
    { stream0: 0.4, stream1: 0.2, stream2: 0.1 },
    { stream0: 0.2, stream1: 0.1, stream2: 0.05 },
    { stream0: 0.1, stream1: 0.05, stream2: 0.02 },
    { stream0: 0.05, stream1: 0.02, stream2: 0.01 },
    { stream0: 0.8, stream1: 0.4, stream2: 0.2 },
    { stream0: 1.2, stream1: 0.6, stream2: 0.3 },
    { stream0: 0.6, stream1: 0.3, stream2: 0.15 },
    { stream0: 0.3, stream1: 0.15, stream2: 0.08 },
    { stream0: 0.1, stream1: 0.05, stream2: 0.02 },
    { stream0: 2.5, stream1: 1.25, stream2: 0.62 },
    { stream0: 2.2, stream1: 1.1, stream2: 0.55 },
    { stream0: 1.8, stream1: 0.9, stream2: 0.45 },
    { stream0: 1.4, stream1: 0.7, stream2: 0.35 },
    { stream0: 1.0, stream1: 0.5, stream2: 0.25 },
  ];

  const statusCodeData = [
    { color: "success-500", label: "200 OK", percentage: "68.8%" },
    { color: "info-500", label: "200 Processed", percentage: "24.6%" },
    { color: "danger-500", label: "400 Bad Request", percentage: "4.1%" },
    { color: "warning-500", label: "500 Internal S...", percentage: "2.5%" },
  ];

  return (
    <>
      <MasterLayout>
        <section className="row">
          <Breadcrumb title="Tổng quan" />
          <TimeRangeFilter
            timeRangeOptions={timeRangeOptions}
            selectedTimeRange={selectedTimeRange}
            setSelectedTimeRange={setSelectedTimeRange}
          />
          <StatisticsCards statisticsCards={statisticsCards} />
        </section>
        <section>
          <div className="row">
            <BarChart chartData={chartData} />
            <StatusPieChart statusCodeData={statusCodeData} />
          </div>
        </section>
      </MasterLayout>
    </>
  );
};

export default DashBoard;
