import { BrowserRouter, Route, Routes } from "react-router-dom";
import "./assets/css/style.css";
import "./assets/css/extra.css";
import "./assets/css/main.css";
import RouteScrollToTop from "./helper/RouteScrollToTop";
import "./utils/apiTest"; // Import for browser console testing

// Components
import DashBoard from "./pages/DashBoard";
import Login from "./pages/Login";
import Profile from "./pages/Profile";
import ForgotPassword from "./pages/ForgotPassword";
import ResetPassword from "./pages/ResetPassword";
import AuthLayout from "./layouts/AuthLayout";
import ProtectedLayout from "./layouts/ProtectedLayout";

function App() {
  return (
    <BrowserRouter>
      <RouteScrollToTop />
      <Routes>
        {/* Auth routes - redirect to dashboard if already logged in */}
        <Route path="/" element={<AuthLayout />}>
          <Route index element={<Login />} />
          <Route path="login" element={<Login />} />
          <Route path="forgot-password" element={<ForgotPassword />} />
          <Route path="reset-password" element={<ResetPassword />} />
        </Route>

        {/* Protected routes - require authentication */}
        <Route path="/" element={<ProtectedLayout />}>
          <Route path="dashboard" element={<DashBoard />} />
          <Route path="profile" element={<Profile />} />
        </Route>
      </Routes>
    </BrowserRouter>
  );
}

export default App;
